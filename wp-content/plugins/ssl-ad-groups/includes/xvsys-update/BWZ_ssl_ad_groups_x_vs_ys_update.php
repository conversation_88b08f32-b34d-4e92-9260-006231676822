<?php

class BWZ_ssl_ad_groups_x_vs_ys_update {

	private $ch;
	private $api_url;
	private $file_name;
	private $file_content;
	private $file_path;
	private $x_vs_ys;
	private $status_get_from_ssl = true;
	private $taxonomy;
	private $x_vs_y_acf_id;
	private $x_vs_y_acf_provider_id;
	private $x_vs_y_acf_kv;
	private $x_vs_y_acf_primary_category;

	public function __construct( $site_abbreviation ) {
		if ( 'production' === wp_get_environment_type() ) {
			$this->api_url = "https://api.softwareselect.com/v1/xvsy/" . $site_abbreviation;
		} else {
			$this->api_url = "https://api-dev.softwareselect.com/v1/xvsy/" . $site_abbreviation;
		}

		$this->file_name                   = $site_abbreviation . '_' . time() . '.json';
		$this->taxonomy                    = BWZ_ssl_ad_groups_custom_field_ssl_x_vs_y::$taxonomy;
		$this->x_vs_y_acf_id               = BWZ_ssl_ad_groups_custom_field_ssl_x_vs_y::$x_vs_y_acf_id;
		$this->x_vs_y_acf_provider_id      = BWZ_ssl_ad_groups_custom_field_ssl_x_vs_y::$x_vs_y_acf_provider_id;
		$this->x_vs_y_acf_kv               = BWZ_ssl_ad_groups_custom_field_ssl_x_vs_y::$x_vs_y_acf_kv;
		$this->x_vs_y_acf_primary_category = BWZ_ssl_ad_groups_custom_field_ssl_x_vs_y::$x_vs_y_acf_primary_category;
		$this->setupFilePath();
		$this->setUpCurl();
	}

	public function init(): void {
		$this->getXvsYsFromSSL();
		$this->upsertXvsYsSSL();
		$this->saveResultFile();
	}

	private function setupFilePath(): void {
		require_once( ABSPATH . 'wp-admin/includes/file.php' );
		global $wp_filesystem;
		WP_Filesystem();

		// Set up the storage directory if necessary.
		$this->file_path = trailingslashit( $wp_filesystem->wp_content_dir() . 'uploads/bawz_local_ssl/x_vs_ys_update/' );
		if ( ! $wp_filesystem->exists( $this->file_path ) ) {
			$wp_filesystem->mkdir( $this->file_path );
		}
	}

	private function setUpCurl(): void {
		$this->ch = curl_init();
		curl_setopt( $this->ch, CURLOPT_RETURNTRANSFER, 1 );
		curl_setopt( $this->ch, CURLOPT_FAILONERROR, true );
		curl_setopt( $this->ch, CURLOPT_URL, $this->api_url );
		curl_setopt( $this->ch, CURLOPT_SSL_VERIFYHOST, false );
		curl_setopt( $this->ch, CURLOPT_SSL_VERIFYPEER, false );
	}

	private function getXvsYsFromSSL(): void {
		$this->x_vs_ys = curl_exec( $this->ch );

		$this->file_content = "Status code: " . curl_getinfo( $this->ch, CURLINFO_HTTP_CODE ) . PHP_EOL;
		if ( curl_errno( $this->ch ) ) {
			$this->file_content        .= curl_error( $this->ch );
			$this->status_get_from_ssl = false;
		}
	}

	private function upsertXvsYsSSL(): void {
		require_once( ABSPATH . 'wp-admin/includes/taxonomy.php' );
		if ( $this->status_get_from_ssl ) {
			$x_vs_ys = json_decode( $this->x_vs_ys, true );
			foreach ( $x_vs_ys as $x_vs_y ) {
				$args         = [
					'hide_empty' => false,
					'meta_query' => [
						[
							'key'     => $this->x_vs_y_acf_id,
							'value'   => $x_vs_y['id'],
							'compare' => '='
						]
					]
				];
				$current_term = get_terms( $args );
				if ( $current_term ) {
					$term_id = $current_term[0]->term_id;
					wp_update_term( $term_id, $this->taxonomy, [
						'name' => $x_vs_y['title']
					] );
				} else {
					$new_term = wp_create_term( $x_vs_y['title'], $this->taxonomy );
					$term_id  = $new_term['term_id'];
				}

				$provider_id = explode( '.', $x_vs_y['kv_key'] );

				if ( count( $provider_id ) > 1 ) {
					$provider_id = $provider_id[1];
				} else {
					$provider_id = '0';
				}

				update_term_meta( $term_id, 'slug', $x_vs_y['slug'] . '_' . $x_vs_y['id'] );
				update_term_meta( $term_id, $this->x_vs_y_acf_id, $x_vs_y['id'] );
				update_term_meta( $term_id, $this->x_vs_y_acf_kv, $x_vs_y['kv_key'] );
				update_term_meta( $term_id, $this->x_vs_y_acf_provider_id, $provider_id );
				update_term_meta( $term_id, $this->x_vs_y_acf_primary_category, $x_vs_y['primary_category'] );
			}
		}
	}

	private function saveResultFile(): void {
		if ( curl_getinfo( $this->ch, CURLINFO_HTTP_CODE ) == "200" ) {
			$this->file_content .= $this->x_vs_ys;
		}
		file_put_contents( $this->file_path . $this->file_name, $this->file_content );
		curl_close( $this->ch );
	}
}
