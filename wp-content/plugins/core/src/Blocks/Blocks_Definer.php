<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks;

use DI;
use Tribe\Libs\Container\Definer_Interface;
use Tribe\Project\Blocks\Types\Accordion\Accordion;
use Tribe\Project\Blocks\Types\Accordion_Advanced\Accordion_Advanced;
use Tribe\Project\Blocks\Types\Ad_Slot\Ad_Slot;
use Tribe\Project\Blocks\Types\Advanced_Content_Filter\Advanced_Content_Filter;
use Tribe\Project\Blocks\Types\Animation_Block\Animation_Block;
use Tribe\Project\Blocks\Types\Author_Notes\Author_Notes;
use Tribe\Project\Blocks\Types\Best_Books\Best_Books;
use Tribe\Project\Blocks\Types\Blockquote_with_Avatar\Blockquote_with_Avatar;
use Tribe\Project\Blocks\Types\Buttons\Buttons;
use Tribe\Project\Blocks\Types\Calculator\Calculator;
use Tribe\Project\Blocks\Types\Card_Grid\Card_Grid;
use Tribe\Project\Blocks\Types\Column\Column;
use Tribe\Project\Blocks\Types\Columns\Columns;
use Tribe\Project\Blocks\Types\Content_Chapter\Content_Chapter;
use Tribe\Project\Blocks\Types\Content_Columns\Content_Columns;
use Tribe\Project\Blocks\Types\Content_Loader\Content_Loader;
use Tribe\Project\Blocks\Types\Content_Loop\Content_Loop;
use Tribe\Project\Blocks\Types\Content_Loop_Columns\Content_Loop_Columns;
use Tribe\Project\Blocks\Types\Countdown\Countdown;
use Tribe\Project\Blocks\Types\Disclaimer\Disclaimer;
use Tribe\Project\Blocks\Types\Gallery_Grid\Gallery_Grid;
use Tribe\Project\Blocks\Types\Grid_List\Grid_List;
use Tribe\Project\Blocks\Types\Group_with_Lottie\Group_with_Lottie;
use Tribe\Project\Blocks\Types\Hero\Hero;
use Tribe\Project\Blocks\Types\Inline_Newsletter\Inline_Newsletter;
use Tribe\Project\Blocks\Types\Inline_Recommendations\Inline_Recommendations;
use Tribe\Project\Blocks\Types\Interstitial\Interstitial;
use Tribe\Project\Blocks\Types\Key_Takeaways\Key_Takeaways;
use Tribe\Project\Blocks\Types\Lead_Form\Lead_Form;
use Tribe\Project\Blocks\Types\Links\Links;
use Tribe\Project\Blocks\Types\Listicle\Checklist\Checklist;
use Tribe\Project\Blocks\Types\Listicle\Overview\Overview;
use Tribe\Project\Blocks\Types\Listicle\ssl_3_Pack_Ad\ssl_3_Pack_Ad;
use Tribe\Project\Blocks\Types\Listicle\ssl_Advanced_Listing\ssl_Advanced_Listing;
use Tribe\Project\Blocks\Types\Listicle\ssl_Button\ssl_Button;
use Tribe\Project\Blocks\Types\Listicle\ssl_Checklist\ssl_Checklist;
use Tribe\Project\Blocks\Types\Listicle\ssl_Full_Listing\ssl_Full_Listing;
use Tribe\Project\Blocks\Types\Listicle\ssl_Mega_Listicle\ssl_Mega_Listicle;
use Tribe\Project\Blocks\Types\Listicle\ssl_Overflow\ssl_Overflow;
use Tribe\Project\Blocks\Types\Listicle\ssl_Overview\ssl_Overview;
use Tribe\Project\Blocks\Types\Listicle\ssl_Premium_Listing_Container\ssl_Premium_Listing_Container;
use Tribe\Project\Blocks\Types\Listicle\ssl_Pricing_Table\ssl_Pricing_Table;
use Tribe\Project\Blocks\Types\Listicle\ssl_ProsCons\ssl_ProsCons;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Alternatives\ssl_Provider_Alternatives;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Analyst_Reviews\ssl_Provider_Analyst_Reviews;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Company_History\ssl_Provider_Company_History;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Details\ssl_Provider_Details;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Faqs\ssl_Provider_Faqs;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Info\ssl_Provider_Info;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Market_Fit\ssl_Provider_Market_Fit;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Overview\ssl_Provider_Overview;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Product_Release_Notes\ssl_Provider_Product_Release_Notes;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Related_Links\ssl_Provider_Related_Links;
use Tribe\Project\Blocks\Types\Listicle\ssl_Provider_Summary\ssl_Provider_Summary;
use Tribe\Project\Blocks\Types\Listicle\ssl_Pub_Review_Faqs\ssl_Pub_Review_Faqs;
use Tribe\Project\Blocks\Types\Listicle\ssl_Pub_Review_Introduction\ssl_Pub_Review_Introduction;
use Tribe\Project\Blocks\Types\Listicle\ssl_Pub_Review_Market_Fit\ssl_Pub_Review_Market_Fit;
use Tribe\Project\Blocks\Types\Listicle\ssl_Shortlist\ssl_Shortlist;
use Tribe\Project\Blocks\Types\Listicle\ssl_Simple_List\ssl_Simple_List;
use Tribe\Project\Blocks\Types\Listicle\ssl_Summary\ssl_Summary;
use Tribe\Project\Blocks\Types\Listicle\ssl_Usecases\ssl_Usecases;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Summary\ssl_XvsY_Summary;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Overview\ssl_XvsY_Overview;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Use_Cases\ssl_XvsY_Use_Cases;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Differences\ssl_XvsY_Differences;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Similarities\ssl_XvsY_Similarities;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Feature_Comparison\ssl_XvsY_Feature_Comparison;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Pricing_Comparison\ssl_XvsY_Pricing_Comparison;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Pros_Cons\ssl_XvsY_Pros_Cons;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Video_Comparison\ssl_XvsY_Video_Comparison;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_More_Comparisons\ssl_XvsY_More_Comparisons;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Alternatives\ssl_XvsY_Alternatives;
use Tribe\Project\Blocks\Types\Logos\Logos;
use Tribe\Project\Blocks\Types\Lottie\Lottie;
use Tribe\Project\Blocks\Types\Manual_Ad\Manual_Ad;
use Tribe\Project\Blocks\Types\Media_Text\Media_Text;
use Tribe\Project\Blocks\Types\Notable_User\Notable_User;
use Tribe\Project\Blocks\Types\Our_Methodology\Our_Methodology;
use Tribe\Project\Blocks\Types\Our_Review_Methodology\Our_Review_Methodology;
use Tribe\Project\Blocks\Types\Perfect_Interview\Perfect_Interview;
use Tribe\Project\Blocks\Types\PPL_Ad\PPL_Ad;
use Tribe\Project\Blocks\Types\PPL_Inline\PPL_Inline;
use Tribe\Project\Blocks\Types\PPL_Selector\PPL_Selector;
use Tribe\Project\Blocks\Types\Presentation_Cards\Presentation_Cards;
use Tribe\Project\Blocks\Types\Presentation_List_of_Images\Presentation_List_of_Images;
use Tribe\Project\Blocks\Types\Pricing\Pricing;
use Tribe\Project\Blocks\Types\Promoted_Content\Promoted_Content;
use Tribe\Project\Blocks\Types\Scrollable_Content\Scrollable_Content;
use Tribe\Project\Blocks\Types\Sections_List\Sections_List;
use Tribe\Project\Blocks\Types\Simple_Embed\Simple_Embed;
use Tribe\Project\Blocks\Types\Spacer\Spacer;
use Tribe\Project\Blocks\Types\Table_of_Contents\Table_of_Contents;
use Tribe\Project\Blocks\Types\Product_Cards\Product_Cards;
use Tribe\Project\Blocks\Types\Tab\Tab;
use Tribe\Project\Blocks\Types\Tabs\Tabs;
use Tribe\Project\Blocks\Types\Testimonial\Testimonial;
use Tribe\Project\Blocks\Types\Text_Separator\Text_Separator;
use Tribe\Project\Blocks\Types\Video_Gallery\Video_Gallery;
use Tribe\Project\Blocks\Types\Why_You_Can_Trust\Why_You_Can_Trust;

class Blocks_Definer implements Definer_Interface {

	public const TYPES_JSON     = 'blocks.types_json';
	public const TYPES          = 'blocks.types';
	public const DENY_LIST      = 'blocks.denylist';
	public const CONTROLLER_MAP = 'blocks.controller_map';
	public const STYLES         = 'blocks.style_overrides';

	public function define(): array {
		return [
			self::TYPES_JSON => DI\add( [
				DI\get( Accordion::class ),
				DI\get( Accordion_Advanced::class ),
				DI\get( Ad_Slot::class ),
				DI\get( Advanced_Content_Filter::class ),
				DI\get( Animation_Block::class ),
				DI\get( Author_Notes::class ),
				DI\get( Best_Books::class ),
				DI\get( Blockquote_with_Avatar::class ),
				DI\get( Buttons::class ),
				DI\get( Calculator::class ),
				DI\get( Card_Grid::class ),
				DI\get( Columns::class ),
				DI\get( Column::class ),
				DI\get( Content_Chapter::class ),
				DI\get( Content_Columns::class ),
				DI\get( Content_Loader::class ),
				DI\get( Content_Loop::class ),
				DI\get( Content_Loop_Columns::class ),
				DI\get( Countdown::class ),
				DI\get( Disclaimer::class ),
				DI\get( Gallery_Grid::class ),
				DI\get( Grid_List::class ),
				DI\get( Group_with_Lottie::class ),
				DI\get( Hero::class ),
				DI\get( Inline_Newsletter::class ),
				DI\get( Inline_Recommendations::class ),
				DI\get( Interstitial::class ),
				DI\get( Key_Takeaways::class ),
				DI\get( Lead_Form::class ),
				DI\get( Links::class ),
				DI\get( Logos::class ),
				DI\get( Lottie::class ),
				DI\get( Manual_Ad::class ),
				DI\get( Media_Text::class ),
				DI\get( Notable_User::class ),
				DI\get( Our_Methodology::class ),
				DI\get( Our_Review_Methodology::class ),
				DI\get( Perfect_Interview::class ),
				DI\get( PPL_Ad::class ),
				DI\get( PPL_Inline::class ),
				DI\get( PPL_Selector::class ),
				DI\get( Presentation_Cards::class ),
				DI\get( Presentation_List_of_Images::class ),
				DI\get( Pricing::class ),
				DI\get( Product_Cards::class ),
				DI\get( Promoted_Content::class ),
				DI\get( Sections_List::class ),
				DI\get( Scrollable_Content::class ),
				DI\get( Simple_Embed::class ),
				DI\get( Spacer::class ),
				DI\get( ssl_3_Pack_Ad::class ),
				DI\get( ssl_Advanced_Listing::class ),
				DI\get( ssl_Button::class ),
				DI\get( ssl_Checklist::class ),
				DI\get( ssl_Full_Listing::class ),
				DI\get( ssl_Mega_Listicle::class ),
				DI\get( ssl_Overflow::class ),
				DI\get( ssl_Overview::class ),
				DI\get( ssl_Premium_Listing_Container::class ),
				DI\get( ssl_Provider_Alternatives::class ),
				DI\get( ssl_Provider_Analyst_Reviews::class ),
				DI\get( ssl_Provider_Company_History::class ),
				DI\get( ssl_Provider_Details::class ),
				DI\get( ssl_Provider_Info::class ),
				DI\get( ssl_Provider_Faqs::class ),
				DI\get( ssl_Provider_Market_Fit::class ),
				DI\get( ssl_Provider_Overview::class ),
				DI\get( ssl_Provider_Related_Links::class ),
				DI\get( ssl_Provider_Product_Release_Notes::class ),
				DI\get( ssl_Provider_Summary::class ),
				DI\get( ssl_Pub_Review_Faqs::class ),
				DI\get( ssl_Pub_Review_Introduction::class ),
				DI\get( ssl_Pub_Review_Market_Fit::class ),
				DI\get( ssl_Pricing_Table::class ),
				DI\get( ssl_ProsCons::class ),
				DI\get( ssl_Shortlist::class ),
				DI\get( ssl_Simple_List::class ),
				DI\get( ssl_Summary::class ),
				DI\get( ssl_Usecases::class ),
				DI\get( ssl_XvsY_Summary::class ),
				DI\get( ssl_XvsY_Overview::class ),
				DI\get( ssl_XvsY_Use_Cases::class ),
				DI\get( ssl_XvsY_Differences::class ),
				DI\get( ssl_XvsY_Similarities::class ),
				DI\get( ssl_XvsY_Pricing_Comparison::class ),
				DI\get( ssl_XvsY_Pros_Cons::class ),
				DI\get( ssl_XvsY_Feature_Comparison::class ),
				DI\get( ssl_XvsY_Video_Comparison::class ),
				DI\get( ssl_XvsY_More_Comparisons::class ),
				DI\get( ssl_XvsY_Alternatives::class ),
				DI\get( Table_of_Contents::class ),
				DI\get( Tabs::class ),
				DI\get( Tab::class ),
				DI\get( Testimonial::class ),
				DI\get( Text_Separator::class ),
				DI\get( Video_Gallery::class ),
				DI\get( Why_You_Can_Trust::class ),
				// Legacy Blocks
				DI\get( Checklist::class ),
				DI\get( Overview::class ),
			] ),

			/**
			 * Deny block list that is managed through JS.
			 *
			 * @see: https://developer.wordpress.org/block-editor/reference-guides/filters/block-filters/#using-a-deny-list
			 */
			self::DENY_LIST  => [
				'core/archives',
				'core/buttons',
				'core/button',
				'core/calendar',
				'core/categories',
				'core/columns',
				'core/cover',
				'core/details',
				'core/gallery',
				'core/latest-comments',
				'core/latest-posts',
				'core/media-text',
				'core/more',
				'core/nextpage',
				'core/pullquote',
				'core/rss',
				'core/search',
				'core/social-links',
				'core/spacer',
				'core/tag-cloud',
				'core/verse',
				'adsanity-listicles/bawz-additional-listing',
				'adsanity-listicles/bawz-full-listing',
				'adsanity-listicles/bawz-advanced-listing',
				'adsanity-listicles/bawz-shortlist',
				'adsanity-listicles/bawz-summary-listing',
				'adsanity/ad-group',
				'adsanity/random-ad',
				'adsanity/single-ad',
			],

			/**
			 * An array of block type style overrides
			 *
			 * Each item in the array should be a factory that returns a Block_Style_Override
			 *
			 * TODO: Create a proper thumbnail of the style for the block editor: http://p.tri.be/dmsAwK
			 */
			self::STYLES     => DI\add( [
				DI\factory( static function () {
					return new Block_Style_Override( [ 'core/paragraph' ], [
						[
							'name'  => 't-overline',
							'label' => __( 'Overline', 'tribe' ),
						],
						[
							'name'  => 't-leadin',
							'label' => __( 'Lead-In', 'tribe' ),
						],
					] );
				} ),
				DI\factory( static function () {
					return new Block_Style_Override( [ 'core/list' ], [
						[
							'name'  => 't-list-stylized',
							'label' => __( 'Stylized', 'tribe' ),
						],
					] );
				} ),
			] ),

			Allowed_Blocks::class => DI\create()->constructor( DI\get( self::DENY_LIST ) ),
		];
	}
}
