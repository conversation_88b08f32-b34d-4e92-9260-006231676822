<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Presentation_List_of_Images;

use Tribe\Libs\ACF\Field_Section;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Blocks\Lib\Block_Config_Json;

class Presentation_List_of_Images extends Block_Config_Json {
	public const NAME = 'presentationlistofimages';

	public const SECTION_CONTENT = 's-content';

	public const TITLE              = 'title';
	public const DESCRIPTION        = 'description';
	public const CARDS_INSTRUCTIONS = 'instructions';

	// Cards fields
	public const CARDS             = 'cards';
	public const CARDS_TITLE       = 'cards_title';
	public const CARDS_DESCRIPTION = 'cards_description';
	public const CARDS_IMAGE       = 'cards_image';

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'presentation_list_of_images',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	protected function add_fields(): void {
		//==========================================
		// Content Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::TITLE, [
					'label' => __( 'Title', 'tribe' ),
					'name'  => self::TITLE,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::DESCRIPTION, [
					'label' => __( 'Description', 'tribe' ),
					'name'  => self::DESCRIPTION,
					'type'  => 'wysiwyg',
				] )
			)->add_field( new Field( self::NAME . '_' . self::CARDS_INSTRUCTIONS, [
					'label'   => __( '', 'tribe' ),
					'name'    => self::CARDS_INSTRUCTIONS,
					'type'    => 'message',
					'message' => '<b>To ensure the block displays correctly, please add <u>at least 6 cards</u> for the visual layout to function as expected.</b>',
				] )
			)->add_field(
				$this->get_images_list_field()
			);
	}

	protected function get_images_list_field(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::CARDS, [
			'min'          => 1,
			'layout'       => 'block',
			'name'         => self::CARDS,
			'label'        => __( 'Cards', 'tribe' ),
			'button_label' => __( 'Add Card', 'tribe' ),
		] );

		$repeater->add_field( new Field( self::CARDS_IMAGE, [
				'label'         => __( 'Image', 'tribe' ),
				'type'          => 'image',
				'return_format' => 'id',
				'name'          => self::CARDS_IMAGE,
			] )
		)->add_field( new Field( self::CARDS_TITLE, [
				'label' => __( 'Title', 'tribe' ),
				'type'  => 'text',
				'name'  => self::CARDS_TITLE,
			] )
		)->add_field( new Field( self::CARDS_DESCRIPTION, [
				'label'        => __( 'Content', 'tribe' ),
				'type'         => 'textarea',
				'name'         => self::CARDS_DESCRIPTION,
				'instructions' => __( '20 word maximum', 'tribe' ),
			] )
		);

		return $repeater;
	}

}
