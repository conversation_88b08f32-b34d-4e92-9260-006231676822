<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Presentation_List_of_Images;

use Tribe\Project\Blocks\Types\Base_Model;
use Tribe\Project\Templates\Components\blocks\presentation_list_of_images\Presentation_List_of_Images_Block_Controller;

class Presentation_List_of_Images_Model extends Base_Model {

	/**
	 * @return array
	 */
	public function get_data(): array {
		return [
			Presentation_List_of_Images_Block_Controller::ATTRS       => $this->get_attrs(),
			Presentation_List_of_Images_Block_Controller::CLASSES     => $this->get_classes(),
			Presentation_List_of_Images_Block_Controller::TITLE       => $this->get( Presentation_List_of_Images::TITLE, '' ),
			Presentation_List_of_Images_Block_Controller::DESCRIPTION => $this->get( Presentation_List_of_Images::DESCRIPTION, '' ),
			Presentation_List_of_Images_Block_Controller::CARDS       => $this->get( Presentation_List_of_Images::CARDS, [] ),
		];
	}
}
