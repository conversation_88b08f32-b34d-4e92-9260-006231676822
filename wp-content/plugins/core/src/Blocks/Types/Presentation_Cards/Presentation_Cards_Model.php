<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Presentation_Cards;

use Tribe\Project\Blocks\Types\Base_Model;
use Tribe\Project\Templates\Components\blocks\presentation_cards\Presentation_Cards_Block_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;

class Presentation_Cards_Model extends Base_Model {

	/**
	 * @return array
	 */
	public function get_data(): array {
		return [
			Presentation_Cards_Block_Controller::ATTRS       => $this->get_attrs(),
			Presentation_Cards_Block_Controller::CLASSES     => $this->get_classes(),
			Presentation_Cards_Block_Controller::TITLE       => $this->get( Presentation_Cards::TITLE, '' ),
			Presentation_Cards_Block_Controller::LINK_TEXT   => $this->get( Presentation_Cards::LINK_TEXT, '' ),
			Presentation_Cards_Block_Controller::CTA         => $this->get_link_args(),
			Presentation_Cards_Block_Controller::CTA_STYLE   => $this->get( Presentation_Cards::CTA_STYLE, Presentation_Cards::STYLE_CTA ),
			Presentation_Cards_Block_Controller::HEADING_TAG => $this->get( Presentation_Cards::HEADING_TAG, Presentation_Cards::HEADING_TAG_H2 ),
			Presentation_Cards_Block_Controller::CARDS       => $this->get( Presentation_Cards::CARDS, [] ),
		];
	}

	private function get_link_args(): array {
		$args = $this->get( Presentation_Cards::CTA, [] );

		if ( empty( $args ) ) {
			return [];
		}

		$link = wp_parse_args( $args, [
			'title'  => '',
			'url'    => '',
			'target' => '',
		] );

		return [
			Link_Controller::CONTENT => $link['title'],
			Link_Controller::URL     => $link['url'],
			Link_Controller::TARGET  => $link['target'],
		];
	}

}
