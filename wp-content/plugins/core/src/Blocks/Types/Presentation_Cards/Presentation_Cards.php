<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Presentation_Cards;

use Tribe\Libs\ACF\Field_Section;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Blocks\Lib\Block_Config_Json;

class Presentation_Cards extends Block_Config_Json {
	public const NAME = 'presentationcards';

	public const SECTION_CONTENT = 's-content';

	public const TITLE     = 'title';
	public const LINK_TEXT = 'link_text';
	public const CTA       = 'cta';
	public const CTA_STYLE = 'cta_style';

	public const HEADING_TAG    = 'heading_tag';
	public const HEADING_TAG_H1 = 'h1';
	public const HEADING_TAG_H2 = 'h2';
	public const HEADING_TAG_H3 = 'h3';
	public const HEADING_TAG_H4 = 'h4';

	public const STYLE_PRIMARY   = 'primary';
	public const STYLE_SECONDARY = 'secondary';
	public const STYLE_CTA       = 'cta';

	public const CLASSES = 'classes';

	// Cards fields
	public const CARDS           = 'cards';
	public const CARDS_HIGHLIGHT = 'cards_highlight';
	public const CARDS_TITLE     = 'cards_title';
	public const CARDS_CONTENT   = 'cards_content';

	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::COMPONENT_FILE_NAME => 'presentation_cards',
			Block_Config_Json::LOAD_STYLE          => true,
		] );
	}

	protected function add_fields(): void {
		//==========================================
		// Content Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' ) )
			->add_field( new Field( self::NAME . '_' . self::TITLE, [
					'label' => __( 'Title', 'tribe' ),
					'name'  => self::TITLE,
					'type'  => 'text',
				] )
			)->add_field( new Field( self::NAME . '_' . self::LINK_TEXT, [
					'label'        => __( 'Link Text', 'tribe' ),
					'name'         => self::LINK_TEXT,
					'type'         => 'textarea',
					'instructions' => __( 'This is the text that will be displayed below the cards.', 'tribe' ),
				] )
			)->add_field( new Field( self::CTA, [
					'label' => __( 'Call to Action', 'tribe' ),
					'name'  => self::CTA,
					'type'  => 'link',
				] )
			)->add_field( new Field( self::NAME . '_' . self::CTA_STYLE, [
					'allow_null'    => 0,
					'choices'       => [
						self::STYLE_CTA       => __( 'Text CTA', 'tribe' ),
						self::STYLE_PRIMARY   => __( 'Primary', 'tribe' ),
						self::STYLE_SECONDARY => __( 'Secondary', 'tribe' ),
					],
					'default_value' => self::STYLE_CTA,
					'label'         => __( 'Style', 'tribe' ),
					'layout'        => 'vertical',
					'name'          => self::CTA_STYLE,
					'return_format' => 'value',
					'required'      => 0,
					'type'          => 'button_group',
				] )
			)->add_field( new Field( self::NAME . '_' . self::HEADING_TAG, [
					'label'         => __( 'Heading Tag', 'tribe' ),
					'name'          => self::HEADING_TAG,
					'type'          => 'select',
					'ui'            => 1,
					'multiple'      => 0,
					'allow_null'    => 0,
					'choices'       => [
						self::HEADING_TAG_H1 => self::HEADING_TAG_H1,
						self::HEADING_TAG_H2 => self::HEADING_TAG_H2,
						self::HEADING_TAG_H3 => self::HEADING_TAG_H3,
						self::HEADING_TAG_H4 => self::HEADING_TAG_H4,
					],
					'default_value' => self::HEADING_TAG_H2,
				] )
			)->add_field(
				$this->get_cards_field()
			);
	}

	protected function get_cards_field(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::CARDS, [
			'min'          => 2,
			'max'          => 3,
			'layout'       => 'block',
			'name'         => self::CARDS,
			'label'        => __( 'Cards', 'tribe' ),
			'button_label' => __( 'Add Card', 'tribe' ),
		] );

		$repeater->add_field( new Field( self::CARDS_HIGHLIGHT, [
				'label' => __( 'Highlight', 'tribe' ),
				'type'  => 'text',
				'name'  => self::CARDS_HIGHLIGHT,
			] )
		)->add_field( new Field( self::CARDS_TITLE, [
				'label' => __( 'Title', 'tribe' ),
				'type'  => 'text',
				'name'  => self::CARDS_TITLE,
			] )
		)->add_field( new Field( self::CARDS_CONTENT, [
				'label'        => __( 'Content', 'tribe' ),
				'name'         => self::CARDS_CONTENT,
				'type'         => 'wysiwyg',
				'toolbar'      => 'basic',
				'media_upload' => 0,
			] )
		);

		return $repeater;
	}

}
