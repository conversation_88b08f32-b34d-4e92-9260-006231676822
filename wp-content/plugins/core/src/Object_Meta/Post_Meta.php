<?php
declare( strict_types=1 );

namespace Tribe\Project\Object_Meta;

use Tribe\Libs\ACF\ACF_Meta_Group;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Group;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Post_Types\Post\Post;
use Tribe\Project\Integrations\Memberpress\Memberpress;
use Tribe\Project\Template_Tags\Template_Tags;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;

class Post_Meta extends ACF_Meta_Group {
	use Handles_MemberPress_Permissions;

	public const NAME                           = 'post_meta';
	public const SUBTITLE                       = 'subtitle';
	public const REVIEWED_ON                    = 'reviewed_on';
	public const REVIEWER_CONTRIBUTOR           = 'reviewer_contributor';
	public const ENTITY_TYPE                    = 'entity_type';
	public const ENTITY_TYPE_REVIEWER           = 'reviewer';
	public const ENTITY_TYPE_CONTRIBUTOR        = 'contributor';
	public const USERS                          = 'users';
	public const REVIEWER_AS_COAUTHOR           = 'reviewer_as_coauthor';
	public const MULTIAUTHOR                    = 'multiauthor';
	public const DISCLAIMER                     = 'disclaimer';
	public const HIDE_AUTHOR                    = 'hide_author';
	public const HIDE_ADS                       = 'hide_ads';
	public const HIDE_CONTENT_ADS               = 'hide_content_ads';
	public const HIDE_CONTENT_NEWSLETTERS       = 'hide_content_newsletters';
	public const HIDE_CONTENT_RECOMMENDATIONS   = 'hide_content_recommendations';
	public const HIDE_SIDEBAR_RECOMMENDATIONS   = 'hide_sidebar_recommendations';
	public const HIDE_BOTTOM_RECOMMENDATIONS    = 'hide_bottom_recommendations';
	public const HIDE_DATE                      = 'hide_date';
	public const HIDE_QUICK_SUMMARY             = 'hide_quick_summary';
	public const HIDE_SECTION_JUMP_LINKS        = 'hide_section_jump_links';
	public const SECTION_JUMP_LINKS             = 'section_jump_links';
	public const BLOCK_ID                       = 'block_id';
	public const LABEL                          = 'label';
	public const SIDEBAR_SIZE                   = 'sidebar_size';
	public const SIDEBAR_SIZE_DEFAULT           = 4;
	public const REPLACE_MAIN_AD_FOR_THREE_PACK = 'replace_main_ad_for_three_pack';
	public const REPLACE_COMPLETE_FOOTER        = 'replace_complete_footer';
	public const SECTION_JUMP_LINKS_BUTTON      = 'section_jump_links_button';
	public const HIDE_KEY_TAKEAWAYS             = 'hide_key_takeaways';
	public const GENERATE_KEY_TAKEAWAYS         = 'generate_key_takeaways';
	public const KEY_TAKEAWAYS_CONTENT          = 'key_takeaways_content';
	public const SUMMARY_KEY                    = 'summary_key';
	public const KEY_TAKEAWAY                   = 'key_takeaway';
	public const LISTICLE_MAX_SIZE              = 'listicle_max_size';
	public const LISTICLE_CATEGORY              = 'listicle_category';
	public const TITLE_TEMPLATE                 = 'title_template';
	public const PPL_FOCUSED_CONTENT            = 'ppl_focused_content';

	public function get_keys() {
		return [
			static::DISCLAIMER,
			static::HIDE_AUTHOR,
			static::HIDE_ADS,
			static::HIDE_CONTENT_ADS,
			static::HIDE_CONTENT_NEWSLETTERS,
			static::HIDE_CONTENT_RECOMMENDATIONS,
			static::HIDE_SIDEBAR_RECOMMENDATIONS,
			static::HIDE_BOTTOM_RECOMMENDATIONS,
			static::HIDE_DATE,
			static::HIDE_SECTION_JUMP_LINKS,
			static::SECTION_JUMP_LINKS,
			static::REVIEWED_ON,
			static::REVIEWER_AS_COAUTHOR,
			static::MULTIAUTHOR,
			static::SIDEBAR_SIZE,
			static::REPLACE_MAIN_AD_FOR_THREE_PACK,
			static::REPLACE_COMPLETE_FOOTER,
			static::SUBTITLE,
			static::HIDE_KEY_TAKEAWAYS,
			static::GENERATE_KEY_TAKEAWAYS,
			static::KEY_TAKEAWAYS_CONTENT,
			static::LISTICLE_MAX_SIZE,
			static::LISTICLE_CATEGORY,
			static::TITLE_TEMPLATE,
			static::PPL_FOCUSED_CONTENT,
			static::SHOW_REGWALL,
		];
	}

	public function get_value( $key, $post_id = 'option' ) {
		return parent::get_value( $post_id, $key );
	}

	public function get_group_config(): array {
		$group = new Group( self::NAME, $this->object_types );
		$group->set( 'title', __( 'Post Options', 'tribe' ) );
		$group->set( 'location', [
			[
				[
					'param'    => 'post_type',
					'operator' => '==',
					'value'    => Post::NAME,
				],
			],
		] );

		$group->add_field( $this->get_title_template_field() );
		$group->add_field( $this->get_subtitle_field() );
		$group->add_field( $this->get_reviewed_on_field() );
		$group->add_field( $this->get_reviewer_as_coauthor_field() );
		$group->add_field( $this->get_reviewer_contributor_field() );
		$group->add_field( $this->get_multiauthor_field() );
		$group->add_field( $this->get_hide_quick_summary_field() );
		$group->add_field( $this->get_hide_section_jump_links_field() );
		$group->add_field( $this->get_section_jump_links_button_field() );
		$group->add_field( $this->get_section_jump_links_field() );
		$group->add_field( $this->get_hide_ads_field() );
		$group->add_field( $this->get_disclaimer_field() );
		$group->add_field( $this->get_hide_content_ads_field() );
		$group->add_field( $this->get_hide_content_newsletters_field() );
		$group->add_field( $this->get_hide_content_recommendations_field() );
		$group->add_field( $this->get_hide_sidebar_recommendations_field() );
		$group->add_field( $this->get_hide_bottom_recommendations_field() );
		$group->add_field( $this->get_hide_key_takeaways_field() );
		$group->add_field( $this->get_generate_key_takeaways_field() );
		$group->add_field( $this->get_key_takeaways_content_field() );
		$group->add_field( $this->get_hide_author_field() );
		$group->add_field( $this->get_hide_date_field() );
		$group->add_field( $this->get_sidebar_size_field() );
		$group->add_field( $this->get_replace_main_ad_for_three_pack_field() );
		$group->add_field( $this->get_replace_complete_footer_for_simple_version_field() );
		$group->add_field( $this->get_ppl_focused_content_field() );

		if ( $this->is_regwall_active() ) {
			$group->add_field( $this->get_show_regwall_field() );
		}

		$group->add_field( $this->get_listicle_max_size_field() );
		$group->add_field( $this->get_listicle_category_field() );

		return $group->get_attributes();
	}

	/**
	 * @return Field
	 */
	private function get_title_template_field(): Field {
		$field = new Field( self::NAME . '_' . self::TITLE_TEMPLATE );
		$field->set_attributes( [
			'label'        => __( 'Title Template', 'tribe' ),
			'instructions' => 'Keep it empty to not automate the title update.<br><b style="color:red;">This will NOT update the slug.</b><br><br>Available Tags:<br><code>' . Template_Tags::TEMPLATE_TAG_YEAR . '</code> to add the current year number ' . date( "Y" ) . '<br><code>' . Template_Tags::TEMPLATE_TAG_NEXT_YEAR . '</code> to add the next year number ' . date( 'Y', strtotime( '+1 year' ) ) . '<br><small>PS.: The title is updated when the post is saved/updated AND in the first day of the year. After saving the post you need to refresh the page to see the new title.</small>',
			'name'         => self::TITLE_TEMPLATE,
			'type'         => 'text',
		] );

		return $field;
	}

	private function get_subtitle_field(): Field {
		$field = new Field( self::NAME . '_' . self::SUBTITLE );
		$field->set_attributes( [
			'label'        => __( 'Subtitle', 'tribe' ),
			'instructions' => __( 'Appears below the Post title', 'tribe' ),
			'name'         => self::SUBTITLE,
			'type'         => 'text',
		] );

		return $field;
	}

	private function get_reviewed_on_field(): Field {
		$field = new Field( self::NAME . '_' . self::REVIEWED_ON );
		$field->set_attributes( [
			'label'          => __( 'Reviewed on', 'tribe' ),
			'name'           => self::REVIEWED_ON,
			'type'           => 'date_picker',
			'display_format' => 'F j, Y',
			'return_format'  => 'Y-m-d',
			'first_day'      => 1,
			'allow_null'     => true,
			'wrapper'        => [
				'width' => '50%',
			],
		] );

		return $field;
	}

	private function get_reviewer_contributor_field(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::REVIEWER_CONTRIBUTOR );
		$repeater->set_attributes( [
			'label'        => __( 'Add Reviewer/Contributor', 'tribe' ),
			'name'         => self::REVIEWER_CONTRIBUTOR,
			'layout'       => 'block',
			'min'          => 0,
			'max'          => 2,
			'button_label' => __( 'Add new entity', 'tribe' ),
		] );

		$repeater->add_field( new Field( self::ENTITY_TYPE, [
			'label'         => __( 'Type', 'tribe' ),
			'name'          => self::ENTITY_TYPE,
			'type'          => 'select',
			'choices'       => [
				self::ENTITY_TYPE_REVIEWER    => __( 'Reviewer', 'tribe' ),
				self::ENTITY_TYPE_CONTRIBUTOR => __( 'Contributor', 'tribe' ),
			],
			'default_value' => self::ENTITY_TYPE_REVIEWER,
			'multiple'      => 0,
			'ui'            => 1,
			'allow_null'    => 0,
		] ) );

		$repeater->add_field( new Field( self::USERS, [
			'label'        => __( 'Users', 'tribe' ),
			'name'         => self::USERS,
			'type'         => 'user',
			'allow_null'   => false,
			'multiple'     => true,
			'required'     => true,
			'role'         => [
				0 => 'author',
				1 => 'editor',
				2 => 'administrator',
				4 => 'content_manager',
				5 => 'sales_content_manager',
				6 => 'contributor',
			],
			'instructions' => __( 'You can add multiple reviewers/contributors in this field.', 'tribe' ),
		] ) );

		return $repeater;
	}

	private function get_reviewer_as_coauthor_field(): Field {
		$field = new Field( self::NAME . '_' . self::REVIEWER_AS_COAUTHOR );
		$field->set_attributes( [
			'label'         => __( 'Add Reviewer as co-author?', 'tribe' ),
			'name'          => self::REVIEWER_AS_COAUTHOR,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'wrapper'       => [
				'width' => '50%',
			],
		] );

		return $field;
	}

	private function get_multiauthor_field(): Field {
		$field = new Field( self::NAME . '_' . self::MULTIAUTHOR );
		$field->set_attributes( [
			'label'         => __( 'Multiauthor selector', 'tribe' ),
			'name'          => self::MULTIAUTHOR,
			'type'          => 'user',
			'allow_null'    => true,
			'multiple'      => true,
			'return_format' => 'id',
			'role'          => [
				0 => 'author',
				1 => 'editor',
				2 => 'administrator',
				4 => 'content_manager',
				5 => 'sales_content_manager',
				6 => 'contributor',
			],
		] );

		return $field;
	}

	/**
	 * Only posts have the option to hide the author.
	 *
	 * @return Field
	 */
	private function get_hide_author_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_AUTHOR );
		$field->set_attributes( [
			'label'         => __( 'Hide Author?', 'tribe' ),
			'name'          => self::HIDE_AUTHOR,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * Only Posts have the option to hide the Ads.
	 *
	 * @return Field
	 */
	private function get_hide_ads_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_ADS );
		$field->set_attributes( [
			'label'         => __( 'Sponsored Content?', 'tribe' ),
			'name'          => self::HIDE_ADS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'instructions'  => __( 'This setting hides all ads on the site and displays a disclaimer.', 'tribe' ),
		] );

		return $field;
	}

	/**
	 * Only Posts have the disclaimer field.
	 *
	 * @return Field
	 */
	private function get_disclaimer_field(): Field {
		$field = new Field( self::NAME . '_' . self::DISCLAIMER );
		$field->set_attributes( [
			'label'             => __( 'Disclaimer', 'tribe' ),
			'name'              => self::DISCLAIMER,
			'type'              => 'text',
			'required'          => true,
			'conditional_logic' => [
				[
					[
						'field'    => 'field_' . self::NAME . '_' . self::HIDE_ADS,
						'operator' => '==',
						'value'    => '1',
					],
				],
			],
		] );

		return $field;
	}

	/**
	 * Only posts can hide the content ads.
	 *
	 * @return Field
	 */
	private function get_hide_content_ads_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_CONTENT_ADS );
		$field->set_attributes( [
			'label'         => __( 'Hide In-Content Ads?', 'tribe' ),
			'name'          => self::HIDE_CONTENT_ADS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'instructions'  => __( 'This setting hides only the in-content ad.', 'tribe' ),
		] );

		return $field;
	}

	/**
	 * Hide the newsletter on the content
	 *
	 * @return Field
	 */
	private function get_hide_content_newsletters_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_CONTENT_NEWSLETTERS );
		$field->set_attributes( [
			'label'         => __( 'Hide In-Content Automatically Newsletters?', 'tribe' ),
			'name'          => self::HIDE_CONTENT_NEWSLETTERS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'instructions'  => __( 'This setting hides only the in-content newsletter. When adding a In-Content Newsletter block in the content, this setting will be ignored.', 'tribe' ),
		] );

		return $field;
	}

	/**
	 * Hide the recommendations on the content
	 *
	 * @return Field
	 */
	private function get_hide_content_recommendations_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_CONTENT_RECOMMENDATIONS );
		$field->set_attributes( [
			'label'         => __( 'Hide In-Content Automatically Recommendations?', 'tribe' ),
			'name'          => self::HIDE_CONTENT_RECOMMENDATIONS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'instructions'  => __( 'This setting hides only the in-content recommendation. When adding a In-Content Recommendations block in the content, this setting will be ignored.', 'tribe' ),
		] );

		return $field;
	}

	/**
	 * Hide the sidebar post recommendations
	 *
	 * @return Field
	 */
	private function get_hide_sidebar_recommendations_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_SIDEBAR_RECOMMENDATIONS );
		$field->set_attributes( [
			'label'         => __( 'Hide Sidebar Post Recommendations?', 'tribe' ),
			'name'          => self::HIDE_SIDEBAR_RECOMMENDATIONS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'instructions'  => __( 'This setting hides the related posts section at the sidebar.', 'tribe' ),
		] );

		return $field;
	}

	/**
	 * Hide the bottom post recommendations
	 *
	 * @return Field
	 */
	private function get_hide_bottom_recommendations_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_BOTTOM_RECOMMENDATIONS );
		$field->set_attributes( [
			'label'         => __( 'Hide Bottom Post Recommendations?', 'tribe' ),
			'name'          => self::HIDE_BOTTOM_RECOMMENDATIONS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'instructions'  => __( 'This setting hides the recommended posts at the bottom of the page.', 'tribe' ),
		] );

		return $field;
	}

	/**
	 * Only posts have the hide publish date field.
	 *
	 * @return Field
	 */
	private function get_hide_date_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_DATE );
		$field->set_attributes( [
			'label'         => __( 'Hide Published Date?', 'tribe' ),
			'name'          => self::HIDE_DATE,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * Posts should have the Hide Quick Summary option.
	 *
	 * @return Field
	 */
	private function get_hide_quick_summary_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_QUICK_SUMMARY );
		$field->set_attributes( [
			'label'         => __( 'Hide Quick Summary?', 'tribe' ),
			'name'          => self::HIDE_QUICK_SUMMARY,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	private function get_hide_section_jump_links_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_SECTION_JUMP_LINKS );
		$field->set_attributes( [
			'label'         => __( 'Hide Table Of Contents?', 'tribe' ),
			'name'          => self::HIDE_SECTION_JUMP_LINKS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * Only posts have jumplinks
	 *
	 * @return Repeater
	 */
	private function get_section_jump_links_field(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::SECTION_JUMP_LINKS );
		$repeater->set_attributes( [
			'label'        => __( 'Table Of Contents links', 'tribe' ),
			'name'         => self::SECTION_JUMP_LINKS,
			'layout'       => 'block',
			'min'          => 0,
			'button_label' => __( 'Add new section', 'tribe' ),
		] );

		$repeater->add_field( new Field( self::BLOCK_ID, [
			'label' => __( 'Block ID', 'tribe' ),
			'name'  => self::BLOCK_ID,
			'type'  => 'text',
		] ) );

		$repeater->add_field( new Field( self::LABEL, [
			'label' => __( 'Label', 'tribe' ),
			'name'  => self::LABEL,
			'type'  => 'text',
		] ) );

		return $repeater;
	}

	private function get_section_jump_links_button_field(): Field {
		$field = new Field( self::NAME . '_' . self::SECTION_JUMP_LINKS_BUTTON );
		$field->set_attributes( [
			'label'   => __( 'Table Of Contents - Options', 'tribe' ),
			'name'    => self::SECTION_JUMP_LINKS_BUTTON,
			'type'    => 'message',
			'message' => '<button class="button button-primary" id="acf-section-jump-links-option-populate-h2">Populate Table Of Contents (H2)</button> <button class="button button-primary" id="acf-section-jump-links-option-populate-h3">Populate Table Of Contents (H2+H3)</button> <button class="button" id="acf-section-jump-links-option-rewrite">Convert Labels to Sentence Case (Uses AI)</button> <button class="button" id="acf-section-jump-links-option-clear">Clear Table Of Contents</button> <span id="acf-section-jump-links-option-anchor"></span>',
		] );

		return $field;
	}

	private function get_hide_key_takeaways_field(): Field {
		$field = new Field( self::NAME . '_' . self::HIDE_KEY_TAKEAWAYS );
		$field->set_attributes( [
			'label'         => __( 'Hide Automatically Key Takeaways?', 'tribe' ),
			'name'          => self::HIDE_KEY_TAKEAWAYS,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
			'instructions'  => __( 'This setting hides the automatic Key Takeaways from the beginning of the content. When adding a Key Takeaway block in the content, this setting will be ignored.', 'tribe' ),
		] );

		return $field;
	}

	private function get_generate_key_takeaways_field(): Field {
		$field = new Field( self::NAME . '_' . self::GENERATE_KEY_TAKEAWAYS );
		$field->set_attributes( [
			'label'   => __( 'Generate Key Takeaways with AI', 'tribe' ),
			'name'    => self::GENERATE_KEY_TAKEAWAYS,
			'type'    => 'message',
			'message' => '<p><i>If the post content is too long, then there multiple requests will be sent to OpenAI, so generation time can take more time than usual.</i></p><button class="button button-primary" id="acf-key-takeaways-generate">Generate Key Takeaways</button> <button class="button" id="acf-key-takeaways-clear">Clear Key Takeaways</button>',
		] );

		return $field;
	}

	/**
	 * @return Repeater
	 */
	private function get_key_takeaways_content_field(): Repeater {
		$repeater = new Repeater( self::NAME . '_' . self::KEY_TAKEAWAYS_CONTENT );
		$repeater->set_attributes( [
			'label'        => __( 'Key Takeaways', 'tribe' ),
			'name'         => self::KEY_TAKEAWAYS_CONTENT,
			'layout'       => 'block',
			'min'          => 0,
			'max'          => 5,
			'button_label' => __( 'Add new Key Takeaway', 'tribe' ),
		] );

		$repeater->add_field( new Field( self::SUMMARY_KEY, [
			'label' => __( 'Title', 'tribe' ),
			'name'  => self::SUMMARY_KEY,
			'type'  => 'text',
		] ) );

		$repeater->add_field( new Field( self::KEY_TAKEAWAY, [
			'label' => __( 'Key Takeaway', 'tribe' ),
			'name'  => self::KEY_TAKEAWAY,
			'type'  => 'text',
		] ) );

		return $repeater;
	}

	private function get_sidebar_size_field(): Field {
		$field = new Field( self::NAME . '_' . self::SIDEBAR_SIZE );
		$field->set_attributes( [
			'label'         => __( 'Customize Sidebar Size', 'tribe' ),
			'instructions'  => __( 'Change the maximum number of blocks displayed in the sidebar.<br>If there is not enough content to fit the size, the number will be automatically reduced.', 'tribe' ),
			'name'          => self::SIDEBAR_SIZE,
			'type'          => 'number',
			'default_value' => self::SIDEBAR_SIZE_DEFAULT,
			'min'           => 1,
			'max'           => Post_Sidebar_Settings_Meta::POST_SIDEBAR_MAX,
		] );

		return $field;
	}

	/**
	 * The main header will be replaced by a three pack ad
	 *
	 * @return Field
	 */
	private function get_replace_main_ad_for_three_pack_field(): Field {
		$field = new Field( self::NAME . '_' . self::REPLACE_MAIN_AD_FOR_THREE_PACK );
		$field->set_attributes( [
			'label'         => __( 'Replace Main Ad for 3-Pack Ad?', 'tribe' ),
			'name'          => self::REPLACE_MAIN_AD_FOR_THREE_PACK,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * The main header will be replaced by a three pack ad
	 *
	 * @return Field
	 */
	private function get_replace_complete_footer_for_simple_version_field(): Field {
		$field = new Field( self::NAME . '_' . self::REPLACE_COMPLETE_FOOTER );
		$field->set_attributes( [
			'label'         => __( 'Replace Complete Footer for Simple Version?', 'tribe' ),
			'name'          => self::REPLACE_COMPLETE_FOOTER,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_listicle_max_size_field(): Field {
		$field = new Field( self::NAME . '_' . self::LISTICLE_MAX_SIZE );
		$field->set_attributes( [
			'label'         => __( 'Listicle Max Size', 'tribe' ),
			'instructions'  => __( 'This is based in the size of the overflow and this will be automatically updated when the post is saved.<br>(Refresh after updating the post.)', 'tribe' ),
			'name'          => self::LISTICLE_MAX_SIZE,
			'type'          => 'number',
			'default_value' => 0,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_listicle_category_field(): Field {
		$field = new Field( self::NAME . '_' . self::LISTICLE_CATEGORY );
		$field->set_attributes( [
			'label'         => __( 'SSL Listicle (WPP ID)', 'tribe' ),
			'instructions'  => __( 'This is based in the SSL Listicle selected in the first Shortlist in the content and this will be automatically updated when the post is saved.<br>(Refresh after updating the post.)', 'tribe' ),
			'name'          => self::LISTICLE_CATEGORY,
			'type'          => 'number',
			'default_value' => 0,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_ppl_focused_content_field(): Field {
		$field = new Field( self::NAME . '_' . self::PPL_FOCUSED_CONTENT );
		$field->set_attributes( [
			'label'         => __( 'PPL Focused Content', 'tribe' ),
			'instructions'  => __( 'This will replace the Newsletter CTAs for PPL Forms in the header, sidebar, and inline, if the PPL Options > PPL is active in this post.', 'tribe' ),
			'name'          => self::PPL_FOCUSED_CONTENT,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}

	/**
	 * @return Field
	 */
	private function get_show_regwall_field(): Field {
		$field = new Field( self::NAME . '_' . self::SHOW_REGWALL );
		$field->set_attributes( [
			'label'         => __( 'Enable Regwall', 'tribe' ),
			'instructions'  => __( 'This will hide the content for non-members after 3 paragraph blocks. The MemberPress rule needs to be setted up in the settings page.', 'tribe' ),
			'name'          => self::SHOW_REGWALL,
			'type'          => 'true_false',
			'default_value' => 0,
			'ui'            => 1,
		] );

		return $field;
	}
}
