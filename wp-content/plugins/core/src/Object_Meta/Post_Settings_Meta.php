<?php
declare( strict_types=1 );

namespace Tribe\Project\Object_Meta;

use Tribe\Libs\ACF\ACF_Meta_Group;
use Tribe\Libs\ACF\Field;
use Tribe\Libs\ACF\Group;
use Tribe\Libs\ACF\Repeater;
use Tribe\Project\Templates\Components\Traits\Gravity_Forms_Choices;
use Tribe\Project\Templates\Components\Traits\With_Regwall_Fields;
use Tribe\Project\Theme\Config\Image_Sizes;
use Tribe\Project\Integrations\Memberpress\Memberpress;

class Post_Settings_Meta extends ACF_Meta_Group {
	use Gravity_Forms_Choices;
	use With_Regwall_Fields;

	public const NAME = 'post_settings_meta';

	public const AD_GROUP_HEADER_SCROLL             = 'ad_group_header_scroll';
	public const AD_GROUP_POST_HEADER               = 'ad_group_post_header';
	public const AD_GROUP_INLINE                    = 'ad_group_inline';
	public const AD_GROUP_FOOTER                    = 'ad_group_footer';
	public const AD_GROUP_CONTENT_SIDEBAR           = 'ad_group_content_sidebar';
	public const SSL_AD_GROUP_THREE_PACK            = 'ssl_ad_group_three_pack';
	public const DEFAULT_NEWSLETTER_FORM            = 'default_newsletter_form';
	public const DEFAULT_NEWSLETTER_TAG_TITLE       = 'default_newsletter_tag_title';
	public const DEFAULT_NEWSLETTER_CONTENT         = 'default_newsletter_content';
	public const DEFAULT_NEWSLETTER_IMAGE           = 'default_newsletter_image';
	public const DEFAULT_NEWSLETTER_LOTTIE_JSON_URL = 'default_newsletter_lottie_json_url';
	public const HEADER_CTA                         = 'header_cta_translations';
	public const HEADER_CTA_LANGUAGE                = 'header_cta_language';
	public const HEADER_CTA_TEXT                    = 'header_cta_text';
	public const HEADER_CTA_LINK                    = 'header_cta_link';
	public const PPL_FOCUSED_HEADER_CTA_TEXT        = 'ppl_focused_header_cta_text';
	public const PPL_FOCUSED_HEADER_CTA_LINK        = 'ppl_focused_header_cta_link';
	public const DEFAULT_LANGUAGE                   = 'en';

	public function get_keys() {
		return [
			static::AD_GROUP_HEADER_SCROLL,
			static::AD_GROUP_POST_HEADER,
			static::AD_GROUP_INLINE,
			static::AD_GROUP_FOOTER,
		];
	}

	public function get_value( $key, $post_id = 'option' ) {
		return parent::get_value( $post_id, $key );
	}

	public function get_group_config(): array {
		$group = new Group( self::NAME, $this->object_types );
		$group->set( 'title', __( 'Post Settings', 'tribe' ) );

		$group->add_field( $this->get_ad_group_field_header() );
		$group->add_field( $this->get_ad_group_field_post() );
		$group->add_field( $this->get_ad_group_field_inline() );
		$group->add_field( $this->get_ad_group_field_footer() );
		$group->add_field( $this->get_ad_group_field_content_sidebar() );

		$group->add_field( $this->get_ssl_ad_group_three_pack() );

		$group->add_field( $this->get_default_newsletter_form_field() );
		$group->add_field( $this->get_default_newsletter_tag_title_field() );
		$group->add_field( $this->get_default_newsletter_content_field() );
		$group->add_field( $this->get_default_newsletter_image_field() );
		$group->add_field( $this->get_default_newsletter_lottie_json_url_field() );

		$group->add_field( $this->get_header_cta_translated_field() );

		$group = $this->append_regwall_fields( $group );

		return $group->get_attributes();
	}

	private function get_ad_group_field_header(): Field {
		$field = new Field( self::NAME . '_' . self::AD_GROUP_HEADER_SCROLL );
		$field->set_attributes( [
			'label'             => __( 'Header Scroll Ad Group', 'tribe' ),
			'name'              => self::NAME . '_' . self::AD_GROUP_HEADER_SCROLL,
			'type'              => 'taxonomy',
			'instructions'      => '',
			'required'          => 0,
			'conditional_logic' => 0,
			'taxonomy'          => 'ad-group',
			'field_type'        => 'select',
			'allow_null'        => 1,
			'add_term'          => 0,
			'save_terms'        => 0,
			'load_terms'        => 0,
			'return_format'     => 'id',
			'multiple'          => 0,
		] );

		return $field;
	}

	private function get_ad_group_field_post(): Field {
		$field = new Field( self::NAME . '_' . self::AD_GROUP_POST_HEADER );
		$field->set_attributes( [
			'label'             => __( 'Post Header Ad Group', 'tribe' ),
			'name'              => self::NAME . '_' . self::AD_GROUP_POST_HEADER,
			'type'              => 'taxonomy',
			'instructions'      => '',
			'required'          => 0,
			'conditional_logic' => 0,
			'taxonomy'          => 'ad-group',
			'field_type'        => 'select',
			'allow_null'        => 1,
			'add_term'          => 0,
			'save_terms'        => 0,
			'load_terms'        => 0,
			'return_format'     => 'id',
			'multiple'          => 0,
		] );

		return $field;
	}

	private function get_ad_group_field_inline(): Field {
		$field = new Field( self::NAME . '_' . self::AD_GROUP_INLINE );
		$field->set_attributes( [
			'label'             => __( 'Inline Content Ad Group', 'tribe' ),
			'name'              => self::NAME . '_' . self::AD_GROUP_INLINE,
			'type'              => 'taxonomy',
			'instructions'      => '',
			'required'          => 0,
			'conditional_logic' => 0,
			'taxonomy'          => 'ad-group',
			'field_type'        => 'select',
			'allow_null'        => 1,
			'add_term'          => 0,
			'save_terms'        => 0,
			'load_terms'        => 0,
			'return_format'     => 'id',
			'multiple'          => 0,
		] );

		return $field;
	}

	private function get_ad_group_field_footer(): Field {
		$field = new Field( self::NAME . '_' . self::AD_GROUP_FOOTER );
		$field->set_attributes( [
			'label'             => __( 'Footer Ad Group', 'tribe' ),
			'name'              => self::NAME . '_' . self::AD_GROUP_FOOTER,
			'type'              => 'taxonomy',
			'instructions'      => '',
			'required'          => 0,
			'conditional_logic' => 0,
			'taxonomy'          => 'ad-group',
			'field_type'        => 'select',
			'allow_null'        => 1,
			'add_term'          => 0,
			'save_terms'        => 0,
			'load_terms'        => 0,
			'return_format'     => 'id',
			'multiple'          => 0,
		] );

		return $field;
	}

	private function get_ad_group_field_content_sidebar(): Field {
		$field = new Field( self::NAME . '_' . self::AD_GROUP_CONTENT_SIDEBAR );
		$field->set_attributes( [
			'label'             => __( 'Content Sidebar Ad Group', 'tribe' ),
			'name'              => self::NAME . '_' . self::AD_GROUP_CONTENT_SIDEBAR,
			'type'              => 'taxonomy',
			'instructions'      => '',
			'required'          => 0,
			'conditional_logic' => 0,
			'taxonomy'          => 'ad-group',
			'field_type'        => 'select',
			'allow_null'        => 1,
			'add_term'          => 0,
			'save_terms'        => 0,
			'load_terms'        => 0,
			'return_format'     => 'id',
			'multiple'          => 0,
		] );

		return $field;
	}

	private function get_ssl_ad_group_three_pack(): Field {
		$field = new Field( self::NAME . '_' . self::SSL_AD_GROUP_THREE_PACK );
		$field->set_attributes( [
			'label'             => __( 'Default Three Pack Ad', 'tribe' ),
			'name'              => self::NAME . '_' . self::SSL_AD_GROUP_THREE_PACK,
			'type'              => 'taxonomy',
			'instructions'      => '',
			'required'          => 0,
			'conditional_logic' => 0,
			'taxonomy'          => 'ssl-ad-group',
			'field_type'        => 'select',
			'allow_null'        => 1,
			'add_term'          => 0,
			'save_terms'        => 0,
			'load_terms'        => 0,
			'return_format'     => 'id',
			'multiple'          => 0,
		] );

		return $field;
	}

	private function get_default_newsletter_form_field(): Field {
		$field = new Field( self::NAME . '_' . self::DEFAULT_NEWSLETTER_FORM );
		$field->set_attributes( [
			'label'   => __( 'Default Newsletter Form', 'tribe' ),
			'name'    => self::DEFAULT_NEWSLETTER_FORM,
			'type'    => 'select',
			'choices' => $this->get_gravity_forms_choices(),
			'ui'      => 1,
		] );

		return $field;
	}

	private function get_default_newsletter_tag_title_field(): Field {
		$field = new Field( self::NAME . '_' . self::DEFAULT_NEWSLETTER_TAG_TITLE );
		$field->set_attributes( [
			'label' => __( 'Default Newsletter Tag Title', 'tribe' ),
			'name'  => self::DEFAULT_NEWSLETTER_TAG_TITLE,
			'type'  => 'text',
		] );

		return $field;
	}

	private function get_default_newsletter_content_field(): Field {
		$field = new Field( self::NAME . '_' . self::DEFAULT_NEWSLETTER_CONTENT );
		$field->set_attributes( [
			'label' => __( 'Default Newsletter Content Line', 'tribe' ),
			'name'  => self::DEFAULT_NEWSLETTER_CONTENT,
			'type'  => 'text',
		] );

		return $field;
	}

	private function get_default_newsletter_image_field(): Field {
		$field = new Field( self::NAME . '_' . self::DEFAULT_NEWSLETTER_IMAGE );
		$field->set_attributes( [
			'label'         => __( 'Default Newsletter Image', 'tribe' ),
			'name'          => self::DEFAULT_NEWSLETTER_IMAGE,
			'type'          => 'image',
			'return_format' => 'id',
			'preview_size'  => Image_Sizes::SQUARE_MEDIUM,
		] );

		return $field;
	}

	private function get_default_newsletter_lottie_json_url_field(): Field {
		$field = new Field( self::NAME . '_' . self::DEFAULT_NEWSLETTER_LOTTIE_JSON_URL );
		$field->set_attributes( [
			'label' => __( 'Default Lottie JSON URL', 'tribe' ),
			'name'  => self::DEFAULT_NEWSLETTER_LOTTIE_JSON_URL,
			'type'  => 'url',
		] );

		return $field;
	}

	private function get_header_cta_translated_field(): Repeater {
		$cta_repeater = new Repeater( self::NAME . '_' . self::HEADER_CTA, [
			'label'        => __( 'Header CTA', 'tribe' ),
			'name'         => self::HEADER_CTA,
			'button_label' => __( 'Add Translation', 'tribe' ),
			'type'         => 'repeater',
			'layout'       => 'block',
		] );

		$cta_repeater->add_field( new Field( self::NAME . '_' . self::HEADER_CTA . '_' . self::HEADER_CTA_LANGUAGE, [
			'label'         => __( 'Language', 'tribe' ),
			'name'          => self::HEADER_CTA_LANGUAGE,
			'required'      => true,
			'type'          => 'select',
			'default_value' => self::DEFAULT_LANGUAGE,
			'choices'       => [
				'en' => 'English',
				'es' => 'Español',
				'de' => 'Deutsch',
				'fr' => 'Français',
			],
		] ) );

		$cta_repeater->add_field( new Field( self::NAME . '_' . self::HEADER_CTA . '_' . self::HEADER_CTA_TEXT, [
			'label'    => __( 'Title', 'tribe' ),
			'name'     => self::HEADER_CTA_TEXT,
			'required' => true,
			'type'     => 'text',
		] ) );

		$cta_repeater->add_field( new Field( self::NAME . '_' . self::HEADER_CTA . '_' . self::HEADER_CTA_LINK, [
			'label'    => __( 'Link', 'tribe' ),
			'name'     => self::HEADER_CTA_LINK,
			'required' => true,
			'type'     => 'link',
		] ) );

		$cta_repeater->add_field( new Field( self::NAME . '_' . self::HEADER_CTA . '_' . self::PPL_FOCUSED_HEADER_CTA_TEXT, [
			'label' => __( 'PPL Focused Title', 'tribe' ),
			'name'  => self::PPL_FOCUSED_HEADER_CTA_TEXT,
			'type'  => 'text',
		] ) );

		$cta_repeater->add_field( new Field( self::NAME . '_' . self::HEADER_CTA . '_' . self::PPL_FOCUSED_HEADER_CTA_LINK, [
			'label' => __( 'PPL Focused Link', 'tribe' ),
			'name'  => self::PPL_FOCUSED_HEADER_CTA_LINK,
			'type'  => 'link',
		] ) );

		return $cta_repeater;
	}
}
