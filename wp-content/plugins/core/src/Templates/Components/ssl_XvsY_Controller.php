<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components;

use Tribe\Project\Admin\Editor\Blocks_Utilities;
use Tribe\Project\Templates\Models\ssl_Screenshot;
use Tribe\Project\Object_Meta\XvsY_Meta;
use Tribe\Project\Taxonomies\Ssl_X_Vs_Y\Ssl_X_Vs_Y;
use Tribe\Project\Templates\Models\ssl_Provider;
use Tribe\Project\Templates\Models\ssl_Pub_Review;
use BWZ_ssl_ad_groups_custom_field_ssl_x_vs_y;
use WP_Term;

abstract class ssl_XvsY_Controller extends Abstract_Controller {
	public const SSL_XVSY         = 'ssl_xvsy';
	public const KV_KEY           = 'kv_key';
	public const PRIMARY_CATEGORY = 'primary_category';
	public const XVSY             = 'xvsy';
	public const TITLE            = 'title';
	public const INTRODUCTION     = 'introduction';
	public const SUMMARY          = 'summary';
	public const VERDICT          = 'verdict';
	public const POST_ID          = 'post_id';

	private int                 $ssl_xvsy;
	private WP_Term|null        $taxonomy;
	private string              $kv_key;
	private int                 $primary_category;
	private array|null          $xvsy;
	private ssl_Provider|null   $provider_a;
	private ssl_Provider|null   $provider_b;
	private ssl_Pub_Review|null $provider_a_pub_review;
	private ssl_Pub_Review|null $provider_b_pub_review;
	private ssl_Screenshot|null $provider_a_main_screenshot;
	private ssl_Screenshot|null $provider_b_main_screenshot;
	private string              $title;
	private string              $introduction;
	private string              $summary;
	private string              $verdict;
	private array|null          $differences;
	private array|null          $similarities;
	private int|null            $post_id;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->taxonomy                   = null;
		$this->kv_key                     = '';
		$this->primary_category           = 0;
		$this->xvsy                       = null;
		$this->provider_a                 = null;
		$this->provider_b                 = null;
		$this->provider_a_pub_review      = null;
		$this->provider_b_pub_review      = null;
		$this->provider_a_main_screenshot = null;
		$this->provider_b_main_screenshot = null;
		$this->differences                = null;
		$this->similarities               = null;
		$this->post_id                    = ! empty( $args[ self::POST_ID ] ) ? (int) $args[ self::POST_ID ] : null;

		// Fallback to current post XvsY id
		if ( empty( $args[ self::SSL_XVSY ] ) && $this->post_id ) {
			if ( $tool_xvsy = get_field( XvsY_Meta::SSL_XVSY, $this->post_id ) ) {
				$args[ self::SSL_XVSY ] = $tool_xvsy;
			}
		}

		$this->ssl_xvsy = (int) $args[ self::SSL_XVSY ];

		$this->load_xvsy_data();

		if ( ! $this->xvsy ) {
			return;
		}

		$this->title = (string) $this->xvsy[ self::TITLE ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::SSL_XVSY         => 0,
			self::KV_KEY           => '',
			self::PRIMARY_CATEGORY => 0,
			self::XVSY             => [],
		];
	}

	private function load_xvsy_data(): void {
		// Load the XvsY Taxonomy
		$xvsy_taxonomy = get_term( $this->ssl_xvsy, Ssl_X_Vs_Y::NAME );

		if ( ! $xvsy_taxonomy instanceof WP_Term ) {
			return;
		}

		$this->taxonomy = $xvsy_taxonomy;

		// Load the KV Key in the XvsY Taxonomy
		$acf_term_id  = Ssl_X_Vs_Y::NAME . '_' . $xvsy_taxonomy->term_id;
		$kv_key       = get_field( BWZ_ssl_ad_groups_custom_field_ssl_x_vs_y::$x_vs_y_acf_kv, $acf_term_id );
		$this->kv_key = $kv_key ?? '';

		// Load the Primary Category in the XvsY Taxonomy
		$primary_category       = get_field( BWZ_ssl_ad_groups_custom_field_ssl_x_vs_y::$x_vs_y_acf_primary_category, $acf_term_id );
		$this->primary_category = (int) $primary_category ?? 0;

		// Load the XvsY Object from the KV
		$this->xvsy = bawz_get_xvsy_from_kv( $this->kv_key );

		if ( ! $this->xvsy ) {
			$this->provider_a                 = null;
			$this->provider_b                 = null;
			$this->provider_a_pub_review      = null;
			$this->provider_b_pub_review      = null;
			$this->provider_a_main_screenshot = null;
			$this->provider_b_main_screenshot = null;

			return;
		}

		// Load the Provider A from the KV
		if ( array_key_exists( 'provider_a', $this->xvsy ) ) {
			if ( array_key_exists( 'pub_review', $this->xvsy['provider_a'] ) ) {
				$this->provider_a_pub_review = new ssl_Pub_Review( [
					ssl_Pub_Review::PUB_REVIEW => $this->xvsy['provider_a']['pub_review'],
				] );

				if ( array_key_exists( 'screenshot_url', $this->xvsy['provider_a']['pub_review'] ) ) {
					$this->provider_a_main_screenshot = new ssl_Screenshot( [
						ssl_Screenshot::URL     => $this->prepare_image_url_by_env( $this->xvsy['provider_a']['pub_review']['screenshot_url'] ),
						ssl_Screenshot::CAPTION => $this->xvsy['provider_a']['pub_review']['screenshot_caption'],
					] );
				} else {
					$this->provider_a_main_screenshot = null;
				}

				unset( $this->xvsy['provider_a']['pub_review'] );
			} else {
				$this->provider_a_pub_review = null;
			}

			$this->provider_a = new ssl_Provider( [
				ssl_Provider::PROVIDER => $this->xvsy['provider_a'],
			] );
		} else {
			$this->provider_a = null;
		}

		// Load the Provider B from the KV
		if ( array_key_exists( 'provider_b', $this->xvsy ) ) {
			if ( array_key_exists( 'pub_review', $this->xvsy['provider_b'] ) ) {
				$this->provider_b_pub_review = new ssl_Pub_Review( [
					ssl_Pub_Review::PUB_REVIEW => $this->xvsy['provider_b']['pub_review'],
				] );

				if ( array_key_exists( 'screenshot_url', $this->xvsy['provider_b']['pub_review'] ) ) {
					$this->provider_b_main_screenshot = new ssl_Screenshot( [
						ssl_Screenshot::URL     => $this->prepare_image_url_by_env( $this->xvsy['provider_b']['pub_review']['screenshot_url'] ),
						ssl_Screenshot::CAPTION => $this->xvsy['provider_b']['pub_review']['screenshot_caption'],
					] );
				} else {
					$this->provider_b_main_screenshot = null;
				}

				unset( $this->xvsy['provider_b']['pub_review'] );
			} else {
				$this->provider_b_pub_review = null;
			}

			$this->provider_b = new ssl_Provider( [
				ssl_Provider::PROVIDER => $this->xvsy['provider_b'],
			] );
		} else {
			$this->provider_b = null;
		}

		$xvsy_attrs = [ 'introduction', 'summary', 'verdict' ];

		foreach ( $xvsy_attrs as $attr ) {
			$this->{$attr} = $this->xvsy[ $attr ] ?: '';
		}

		$xvsy_array_attrs = [ 'differences', 'similarities' ];

		foreach ( $xvsy_array_attrs as $attr ) {
			$this->{$attr} = ! empty( $this->xvsy[ $attr ] ) && is_array( $this->xvsy[ $attr ] ) ? (array) $this->xvsy[ $attr ] : null;
		}
	}

	public function get_ssl_xvsy(): int {
		return $this->ssl_xvsy;
	}

	public function get_taxonomy(): WP_Term|null {
		return $this->taxonomy;
	}

	public function get_primary_category(): ?int {
		return $this->primary_category;
	}

	public function get_title(): string {
		return $this->title;
	}

	public function get_introduction(): ?string {
		return $this->introduction ?? null;
	}

	public function get_summary(): string {
		return $this->summary;
	}

	public function get_verdict(): ?string {
		return $this->verdict ?? null;
	}

	public function get_provider_a(): ?ssl_Provider {
		return $this->provider_a;
	}

	public function get_provider_b(): ?ssl_Provider {
		return $this->provider_b;
	}

	public function get_provider_a_pub_review(): ?ssl_Pub_Review {
		return $this->provider_a_pub_review;
	}

	public function get_provider_b_pub_review(): ?ssl_Pub_Review {
		return $this->provider_b_pub_review;
	}

	public function get_provider_a_main_screenshot(): ?ssl_Screenshot {
		return $this->provider_a_main_screenshot;
	}

	public function get_provider_b_main_screenshot(): ?ssl_Screenshot {
		return $this->provider_b_main_screenshot;
	}

	private function prepare_image_url_by_env( $value ): string {
		if ( ! $value ) {
			return '';
		}

		if ( wp_get_environment_type() === 'production' ) {
			return $value;
		}

		if ( is_string( $value ) ) {
			$value = str_replace(
				'static-staging.crozdesk.com/web_app_library/',
				'crozdesk-uploads.s3.eu-west-1.amazonaws.com/web_app_library/',
				$value
			);
		}

		return $value;
	}

	public function get_differences(): ?array {
		return $this->differences;
	}

	public function get_similarities(): ?array {
		return $this->similarities;
	}

	/**
	 * @param string $block_name
	 * @param string $interaction
	 *
	 * @return array
	 */
	public function get_provider_a_visit_link_attrs( string $block_name, string $interaction ): array {
		$pub_review = $this->get_provider_a_pub_review();
		$provider = $this->get_provider_a();

		if ( ! $pub_review || ! $provider ) {
			return [];
		}

		return $provider->get_visit_link_attrs( $pub_review->get_click_url(), $block_name, $interaction );
	}

	/**
	 * @param string $block_name
	 * @param string $interaction
	 *
	 * @return array
	 */
	public function get_provider_b_visit_link_attrs( string $block_name, string $interaction ): array {
		$pub_review = $this->get_provider_b_pub_review();
		$provider = $this->get_provider_b();

		if ( ! $pub_review || ! $provider ) {
			return [];
		}

		return $provider->get_visit_link_attrs( $pub_review->get_click_url(), $block_name, $interaction );
	}

	/**
	 * @param string $block_name
	 *
	 * @return array
	 */
	public function get_provider_a_review_link_attrs( string $block_name ): array {
		$provider = $this->get_provider_a();

		if ( ! $provider ) {
			return [];
		}

		return $provider->get_review_link_attrs( $block_name );
	}

	/**
	 * @param string $block_name
	 *
	 * @return array
	 */
	public function get_provider_b_review_link_attrs( string $block_name ): array {
		$provider = $this->get_provider_b();

		if ( ! $provider ) {
			return [];
		}

		return $provider->get_review_link_attrs( $block_name );
	}


	/**
	 * @return null|string
	 */
	public function get_no_providers_error(): ?string {
		return Blocks_Utilities::format_admin_error_message( 'No providers found. Make sure that you have selected a XvsY comparison.' );
	}
}
