/* -----------------------------------------------------------------------------
 *
 * Component: Card
 *
 * ----------------------------------------------------------------------------- */

.c-card {
	display: flex;
	flex-flow: column nowrap;
	background-color: var(--color-white);
	border-radius: var(--border-radius-media);

	.wp-block-group.has-dark-background-color & {

		* {
			color: var(--color-text);
		}
	}

	&__members-only-locked {
		position: absolute;
		background-color: var(--color-neutral-10);
		border-radius: 5px;
		border: solid 1px var(--color-alert-negative);
		padding-block: 2px;
		padding-left: 7px;
		padding-right: 12px;
		top: var(--spacer-20);
		left: var(--spacer-20);
		font-size: var(--font-size-body-xsmall);
		display: flex;
		align-items: center;
		z-index: 1;
		line-height: 1.2;

		.icon {
			color: var(--color-alert-negative);
			font-size: var(--font-size-body-large);
		}

		@media (--viewport-large-max) {
			width: min-content;
		}

		@media (--viewport-medium-max) {
			width: max-content;
		}
	}

	/* CASE: Content Loop is handled a little differently since there is no background to these cards */

	.b-content-loop &,
	.b-content-loop-columns &,
	.b-product-cards & {
		.wp-block-group.has-dark-background-color & {
			* {
				color: var(--color-white);
			}
		}
	}

	/* CASE: Centers all content within the card. */

	&.is-centered-text {
		text-align: center;
	}

	/* CASE: Style: Elevated */

	&.c-card--style-elevated {
		padding: var(--spacer-40) 0;
		box-shadow: var(--box-shadow-30);

		@media (--viewport-large) {
			padding: var(--spacer-60) 0;
		}

		/* CASE: Whole card is a link. */

		&.has-target-link {

			&:hover,
			&:focus-within {
				box-shadow: var(--box-shadow-20-hover);
			}
		}
	}

	/* CASE: Style: Outlined */

	&.c-card--style-outlined {
		padding: var(--spacer-30) 0;
		border: 1px solid var(--color-border);

		/* CASE: Whole card is a link. */

		&.has-target-link {

			&:hover,
			&:focus-within {
				border-color: var(--color-link-hover);
			}
		}
	}

	/* CASE: Style: Inline */

	&.c-card--style-inline {
		display: flex;
		flex-flow: row;
		align-items: flex-start;
		margin-top: 0;
		margin-bottom: 0;
		padding: var(--spacer-20) 0;
		border-bottom: 1px solid var(--color-grey-light);

		/* Need to drop the font size down a bit on the tag for inline cards */

		.t-tag {
			font-size: 11px;
		}

		&:hover {

			.c-card__title {
				color: var(--color-link-hover);
			}
		}
	}

	/* CASE: Style: Inside */
	&.c-card--style-inside {
		position: relative;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;

		&:after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(180deg,rgba(0, 0, 0, 0) 40%, var(--color-background-dark) 100%);
			border-bottom-right-radius: var(--border-radius-media);
    		border-bottom-left-radius: var(--border-radius-media);
			z-index: 0;
		}

		.c-card {
			&__media {
				margin: 0;
			}

			&__content {
				position: absolute;
				bottom: 0;
    			left: 0;
				width: 100%;
				padding: 0 var(--spacer-40) var(--spacer-30);
				text-align: center;
				z-index: 1;

				@media (--viewport-medium-max) {
					padding: 0 var(--spacer-20) var(--spacer-20);
				}
			}

			&__title,
			&__description {
				color: var(--color-white);
			}

			&__title {
				@mixin t-display-x-small;
				font-weight: var(--font-weight-medium);

				&:hover {
					text-decoration: none;
				}
			}

			&__description {
				@mixin t-body-small;
				text-wrap: balance;
				margin-top: 0;

				@media (--viewport-medium-max) {
					margin-bottom: 0;
				}
			}

			&__title:is(:last-child) {
				margin-bottom: var(--spacer-20);
			}
		}
	}

	.b-card-grid & {
		&:hover {

			.c-card__title {
				color: var(--color-primary);
				text-decoration: underline;
			}
		}
	}

	.c-image__link {
		.acf-block-preview & {
			pointer-events: none;
		}
	}

	/* Multi Author post */

	.item-single--multiauthor {
		.item-single__avatars_wrapper {
			display: flex;
		}

		.item-single__author-image {
			&:not(:first-of-type) {
				margin-left: -25px;
			}
		}
	}
}

/* -----------------------------------------------------------------------------
 * Card Media Container
 * ----------------------------------------------------------------------------- */

.c-card__media {
	position: relative;
	margin-bottom: var(--spacer-40);

	&--locked .c-image__image {
		filter: brightness(0.7);
	}

	/* CASE: Style: Outlined & Elevated */

	.c-card--style-elevated &,
	.c-card--style-outlined & {
		margin-top: calc(-1 * var(--spacer-30));
	}

	/* CASE: Style: Inline */

	.c-card--style-inline & {
		display: none;
		flex-shrink: 0;

		@media (--viewport-medium) {
			display: block;
			width: var(--grid-2-col);
			margin-right: var(--spacer-30);
			margin-bottom: 0;
		}
	}
}

/* -----------------------------------------------------------------------------
 * Card Content Container
 * ----------------------------------------------------------------------------- */

.c-card__content {
	margin: 0 0 auto; /* Pushes the CTA to the bottom of the card. */

	/* CASE: Style: Outlined & Elevated */

	.c-card--style-elevated &,
	.c-card--style-outlined & {
		padding: 0 var(--spacer-40);

		@media (--viewport-large) {
			padding: 0 var(--spacer-60);
		}
	}
}

/* -----------------------------------------------------------------------------
 * Card Meta
 * ----------------------------------------------------------------------------- */

.c-card__meta {
	margin-bottom: var(--spacer-10);
}

/* -----------------------------------------------------------------------------
 * Card Meta: Secondary
 * ----------------------------------------------------------------------------- */

.c-card__meta--secondary {
	margin-top: var(--spacer-10);
}

/* -----------------------------------------------------------------------------
 * Card Title
 * ----------------------------------------------------------------------------- */

.c-card__title {
	display: block;
	font-size: var(--font-size-heading-xsmall);

	/* CASE: Style: Inline */

	.c-card--style-inline & {
		display: block;
		text-decoration: none;

		&:focus {
			text-decoration: underline;
		}
	}

	.acf-block-preview & {
		pointer-events: none;
	}

	&:hover,
	&:focus {
		color: var(--color-primary);
		text-decoration: underline;

		.c-card__title-content {
			color: var(--color-primary);
			text-decoration: underline;
		}
	}
}

.c-card__title-content {
	&:hover,
	&:focus {
		color: var(--color-primary);
		text-decoration: underline;
	}
}

/* -----------------------------------------------------------------------------
 * Card Date
 * ----------------------------------------------------------------------------- */

.c-card__date,
.c-card__time {
	@mixin t-label;

	font-weight: var(--font-weight-bold);
	display: flex;
	align-items: center;
	margin-top: var(--spacer-10);

	.c-card--query-type-manual & {
		display: none;
	}

	.icon {
		color: var(--color-primary);
		font-size: var(--font-size-body);
		margin-right: var(--spacer-10);
	}
}

.c-card__date {
	margin-top: var(--spacer-20);
}

.c-card__time {
	margin-top: var(--spacer-20);
}

/* -----------------------------------------------------------------------------
 * Upcoming Event Registration Link
 * ----------------------------------------------------------------------------- */

.c-card__registration-link {
	margin-top: var(--spacer-20);
	margin-bottom: var(--spacer-20);
}

/* -----------------------------------------------------------------------------
 * Card Description
 * ----------------------------------------------------------------------------- */

.c-card__description {

	p {
		@mixin t-body;
	}

	margin: var(--spacer-20) 0;
	word-break: break-word;
}

/* -----------------------------------------------------------------------------
 * Card Call To Action
 * ----------------------------------------------------------------------------- */

.c-card__cta {
	margin-top: var(--spacer-20);

	/* CASE: Style: Outlined & Elevated */

	.c-card--style-elevated &,
	.c-card--style-outlined & {
		padding: 0 var(--spacer-40);

		@media (--viewport-large) {
			padding: 0 var(--spacer-60);
		}
	}

	/* CASE: Inside dark background group block */

	.wp-block-group.has-dark-background-color & {

		a {
			color: var(--color-primary);

			&:hover {
				color: var(--color-primary);
			}

			&:after {
				background-image: url(../../../img/icons/arrow-right.svg);
			}
		}
	}
}

.c-card__meta-primary-container {
	flex-wrap: wrap;
	margin-bottom: var(--spacer-20);
}

.c-card__category-link {
	@mixin t-overline;

	color: var(--color-primary-50);

	&:after {
		margin: 0 10px 0 4px;
	}

	a {
		color: var(--color-primary-50);

		.wp-block-group.has-dark-background-color & {
			color: var(--color-primary);
		}

		.acf-block-preview & {
			pointer-events: none;
		}
	}

	.c-card--topic-hidden & {
		display: none;
	}
}

.c-card__sep-dot {
	content: '';
	height: 5px;
	width: 5px;
	border-radius: 50%;
	background-color: var(--color-secondary);
	display: inline-block;
	position: relative;
	bottom: 3px;
	margin: 0 10px;
}

.c-card__secondary-meta {
	@mixin t-overline;

	font-weight: var(--font-weight-medium);
}

.c-card__author {
	margin-top: var(--spacer-20);
	font-weight: var(--font-weight-semibold);
}

.c-card__members-only-warning {
	@mixin t-label;

	font-weight: var(--font-weight-bold);
	margin-top: var(--spacer-20);
	display: flex;
	align-items: center;

	.c-card--query-type-manual & {
		display: none;
	}

	&:before {
		content: "";
		display: inline-block;
		width: 5px;
		height: 5px;
		border-radius: 50%;
		background-color: var(--color-primary);
		margin-right: var(--spacer-10);
	}
}

.c-card__duration {
	@mixin t-overline;

	font-weight: var(--font-weight-regular);
}

.c-card__author {
	margin-top: var(--spacer-20);
	font-weight: var(--font-weight-medium);
}

.c-card {
	.item-single__author-image-img {
		border-radius: 50%;
	}
}

.c-card__price,
.wp-block-group.has-dark-background-color .c-card__price {
	display: flex;
	align-items: center;
	margin-top: var(--spacer-30);

	&__regular {
		position: relative;
		font-size: var(--font-size-body);
		opacity: 0.5;
		margin-right: var(--spacer-10);

		&:before {
			content: '';
			position: absolute;
			width: 100%;
			height: 1px;
			top: 50%;
			left: 0;
			transform: translateY(-50%);
			background-color: var(--color-black);

			.wp-block-group.has-dark-background-color & {
				background-color: var(--color-white);
			}
		}
	}

	&__discounted {
		font-size: var(--font-size-heading);
		font-weight: var(--font-weight-semibold);
		line-height: var(--line-height-heading-large);
		margin-right: var(--spacer-20);
	}

	&__deal-tag {
		color: var(--color-white);
		font-size: var(--font-size-body-xsmall);
		line-height: var(--line-height-heading-xsmall);
		font-weight: var(--font-weight-bold);
		letter-spacing: 1.68px;
		text-transform: uppercase;
		border-radius: 8px;
		padding: var(--spacer-10) var(--spacer-30);
		background-color: var(--color-background-dark);
		text-align: center;

		.wp-block-group.has-dark-background-color & {
			background-color: var(--global-bg-color-primary);
			color: var(--color-black) !important;
		}
	}
}

.c-card__note,
.wp-block-group.has-dark-background-color .c-card__note {
	display: flex;
	align-items: center;
	gap: var(--spacer-20);
	margin-top: calc(var(--spacer-50) - 2px);

	@media (--viewport-full-max) {
		flex-direction: column;
		align-items: flex-start;
		margin-top: var(--spacer-40);
	}

	.c-card__cta {
		margin-top: 0;
	}

	&__content {
		p {
			font-size: var(--font-size-special-small);
		}
	}
}


.c-card__product-tag {
	margin-bottom: var(--spacer-30);

	p {
		@mixin t-overline;

		font-weight: var(--font-weight-regular);
	}
}
