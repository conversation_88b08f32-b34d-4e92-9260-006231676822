<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\routes\single;

use Tribe\Project\Blocks\Types\Why_You_Can_Trust\Why_You_Can_Trust;
use Tribe\Project\Integrations\Crozdesk\Crozdesk_Category;
use Tribe\Project\Object_Meta\PPL_Options_Meta;
use Tribe\Project\Object_Meta\PPL_Settings;
use Tribe\Project\Object_Meta\Tool_Post_Meta;
use Tribe\Project\Object_Meta\XvsY_Meta;
use Tribe\Project\Object_Meta\User_Meta;
use Tribe\Project\Post_Types\Tool_Post\Tool_Post;
use Tribe\Project\Templates\Models\ssl_Dynamic_CTA_Model;
use Tribe\Project\Templates\Models\ssl_Provider;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\table_of_contents\Table_Of_Contents_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\Traits\Authors_Links;
use Tribe\Project\Templates\Components\Traits\Authors_Utilities;
use Tribe\Project\Templates\Components\Traits\Excerpt;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;
use Tribe\Project\Templates\Components\Traits\Handles_Enclosing_Shortcodes;
use Tribe\Project\Templates\Components\Traits\Page_Title;
use Tribe\Project\Templates\Components\Traits\Tag_Loader;
use WP_Term;

class Single_Tool_XvsY_Layout_Controller extends ssl_XvsY_Controller {
	use Tag_Loader;
	use Page_Title;
	use Excerpt;
	use Authors_Links;
	use Authors_Utilities;
	use Handles_MemberPress_Permissions;
	use Handles_Enclosing_Shortcodes;

	public const X_KEY = 'X';
	public const Y_KEY = 'Y';

	const CTA_PRIMARY   = 'primary';

	/**
	 * @var int|string
	 */
	private string|int $sidebar_id = '';

	public function __construct( array $args = [] ) {
		$xvsy_term = $this->get_field( self::SSL_XVSY );

		if ( $xvsy_term ) {
			$args[ self::SSL_XVSY ] = $xvsy_term;

			parent::__construct( $args );
		}
	}

	/**
	 * Render the header component
	 *
	 * Bypasses the get_header() function to
	 * load our component instead of header.php
	 *
	 * @return void
	 */
	public function render_header(): void {
		do_action( 'get_header', null );
		get_template_part( 'components/document/header/header', 'single' );
	}

	public function get_post_primary_category(): ?WP_Term {
		if ( ! $id = get_the_ID() ) {
			return null;
		}

		$categories = get_the_terms( $id, 'category' );

		if ( function_exists( 'yoast_get_primary_term' ) ) {
			if ( $primary_category_id = yoast_get_primary_term_id( 'category', $id ) ) {
				$categories = [ get_term( $primary_category_id, 'category' ) ];
			}
		}

		return ( is_array( $categories ) && count( $categories ) >= 1 ) ? $categories[0] : null;
	}

	public function get_primary_category_link_args(): ?object {
		if ( $category = $this->get_post_primary_category() ) {
			return (object) [
				'link'  => get_term_link( $category ),
				'label' => $category->name,
			];
		}


		return null;
	}

	private function get_default_author_description( $id ): string {
		return get_the_author_meta( 'user_description', (int) $id );
	}

	private function get_author_description( $id ): string {
		$descriptions = get_field( User_Meta::DESCRIPTION, 'user_' . (int) $id );

		if ( ! $descriptions ) {
			return $this->get_default_author_description( $id );
		}

		$description = array_filter(
			$descriptions,
			function ( $description ) {
				if ( function_exists( 'pll_current_language' ) ) {
					return
						(int) $description[ User_Meta::DESCRIPTION_SITE ] === get_current_blog_id() &&
						$description[ User_Meta::DESCRIPTION_LANGUAGE ] === pll_current_language();
				}

				return
					(int) $description[ User_Meta::DESCRIPTION_SITE ] === get_current_blog_id() &&
					$description[ User_Meta::DESCRIPTION_LANGUAGE ] === User_Meta::DEFAULT_LANGUAGE;
			}
		);

		if ( ! $description ) {
			return $this->get_default_author_description( $id );
		}

		return '<p>' . wp_kses(
			current( $description )[ User_Meta::DESCRIPTION_CONTENT ],
			[
				'a'      => [
					'href'  => [],
					'title' => [],
				],
				'br'     => [],
				'em'     => [],
				'strong' => [],
			]
		) . '</p>';
	}

	public function get_author_args( array $params = [] ): array {
		$id = ! empty( $params['id'] ) ? $params['id'] : get_post_field( 'post_author', get_the_ID() );

		return [
			Author_Controller::AUTHOR_NAME      => Author_Controller::get_author_display_name( $id ),
			Author_Controller::AUTHOR_ID        => $id,
			Author_Controller::AVATAR_SIZE      => $params['size'] ?: 50,
			Author_Controller::SHOW_DESCRIPTION => $params['show_description'] ?: false,
			Author_Controller::AUTHOR_ABOUT     => $this->get_author_description( $id ),
			Author_Controller::SHOW_LINK_NAME   => array_key_exists( 'show_link_name', $params ) ? $params['show_link_name'] : false,
			Author_Controller::SHOW_LINKS_LIST  => $this->get_author_links( (int) $id ),
		];
	}

	public function get_title_args(): array {
		if ( empty( $this->get_page_title() ) ) {
			return [];
		}

		return [
			Text_Controller::TAG     => 'h1',
			Text_Controller::CLASSES => [ 'page-title', 'h1' ],
			Text_Controller::CONTENT => $this->get_page_title(),
		];
	}

	public function get_excerpt_args(): array {
		if ( empty( $this->get_excerpt() ) ) {
			return [];
		}

		return [
			Text_Controller::TAG     => 'p',
			Text_Controller::CONTENT => $this->get_excerpt(),
		];
	}

	public function get_subtitle_args(): array {
		$subtitle = $this->get_field( XvsY_Meta::NAME . '_' . XvsY_Meta::SUBTITLE );

		if ( empty( $subtitle ) ) {
			return [];
		}

		return [
			Text_Controller::TAG     => 'p',
			Text_Controller::CLASSES => [ 'item-single__post-meta__header_subtitle' ],
			Text_Controller::CONTENT => $subtitle,
		];
	}

	public function get_why_trust_cta(): ?Deferred_Component {
		$blocks                        = parse_blocks( get_the_content() );
		$why_you_can_trust_block_found = false;
		$block_id                      = '';

		foreach ( $blocks as $block ) {
			if ( $block['blockName'] === 'acf/whyyoucantrust' ) {
				$why_you_can_trust_block_found = true;

				if ( isset( $block['attrs']['anchor'] ) && ! empty( $block['attrs']['anchor'] ) ) {
					$block_id = $block['attrs']['anchor'];
				} else {
					$block_id = Why_You_Can_Trust::ANCHOR;
				}

				break;
			}
		}

		if ( ! $why_you_can_trust_block_found ) {
			return null;
		}

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::URL     => '#' . $block_id,
			Link_Controller::CONTENT => __( ' Why Trust Our Reviews?', 'tribe' ),
			Link_Controller::CLASSES => [ 'item-single__cta-link--why-trust' ],
		] );
	}

	protected function get_field( string $field ) {
		$id = get_the_ID();

		return $id ? get_field( $field, $id ) : false;
	}

	public function get_multiauthor(): ?string {
		$multiauthor = get_field( Tool_Post_Meta::MULTIAUTHOR );

		if ( ! $multiauthor ) {
			return '';
		}

		$content = '';

		foreach ( $multiauthor as $authorID ) {
			$user = get_user_by( 'ID', $authorID );

			$content .= get_template_part( 'components/author/author', null, $this->get_author_args( [
				'id'               => $authorID,
				'link_id'          => 'multi_' . $authorID,
				'size'             => 120,
				'show_description' => true,
			] ) );
		}

		return $content;
	}

	public function get_reviewer_as_coauthor(): ?string {
		$add_reviewer_as_coauthor = get_field( Tool_Post_Meta::REVIEWER_AS_COAUTHOR );
		$reviewers                = $this->get_reviewers();

		if ( ! $add_reviewer_as_coauthor || ! $reviewers ) {
			return '';
		}

		$content = '';

		foreach ( $reviewers as $reviewer ) {
			$content .= get_template_part( 'components/author/author', null, $this->get_author_args( [
				'id'               => $reviewer['ID'],
				'link_id'          => 'reviewer_' . $reviewer['ID'],
				'size'             => 120,
				'show_description' => true,
			] ) );
		}

		return $content;
	}

	public function get_multiple_authors(): array {
		$add_reviewer_as_coauthor = get_field( Tool_Post_Meta::REVIEWER_AS_COAUTHOR );
		$reviewers                = $this->get_reviewers();
		$multiauthor              = get_field( Tool_Post_Meta::MULTIAUTHOR );

		$authors = [];
		if ( $add_reviewer_as_coauthor && $reviewers ) {
			foreach ( $reviewers as $reviewer ) {
				$authors[] = $reviewer['ID'];
			}
		}

		if ( ! empty( $multiauthor ) ) {
			foreach ( $multiauthor as $authorID ) {
				$authors[] = $authorID;
			}
		}

		return $authors;
	}

	public function hide_section_jump_links(): bool {
		return (bool) $this->get_field( Tool_Post_Meta::HIDE_SECTION_JUMP_LINKS ) ?? false;
	}

	public function get_section_jump_links(): array {
		$fields = $this->get_field( Tool_Post_Meta::SECTION_JUMP_LINKS );

		if ( ! is_array( $fields ) ) {
			return [];
		}

		return ( ! empty( $fields[0][ Tool_Post_Meta::BLOCK_ID ] ) ) ? $fields : [];
	}

	public function get_header_revision_content(): ?string {
		$reviewers   = $this->get_reviewers();
		$reviewed_on = get_field( Tool_Post_Meta::REVIEWED_ON );

		if ( ! $reviewers ) {
			return null;
		}

		$reviewers_names = '';

		foreach ( $reviewers as $key => $reviewer ) {
			$reviewer_name = $this->get_author_tooltip( $reviewer['ID'] );

			if ( $key === count( $reviewers ) - 2 ) {
				$reviewers_names .= $reviewer_name . ' & ';
			} elseif ( $key !== count( $reviewers ) - 1 ) {
				$reviewers_names .= $reviewer_name . ', ';
			} else {
				$reviewers_names .= $reviewer_name;
			}
		}

		if ( $reviewed_on ) {
			$content = sprintf(
			/* translators: %s Name of a user, and %s is the reviewed date */
				'%s %s %s %s',
				__( 'Reviewed by', 'tribe' ),
				$reviewers_names,
				__( 'on', 'tribe' ),
				date( 'M j, Y', strtotime( $reviewed_on ) )
			);
		} else {
			$content = sprintf(
				'%s %s',
				__( 'Reviewed by', 'tribe' ),
				$reviewers_names
			);
		}

		return $content;
	}

	public function get_contributors(): ?array {
		$reviewer_contributor = get_field( Tool_Post_Meta::REVIEWER_CONTRIBUTOR );

		if ( ! $reviewer_contributor ) {
			return [];
		}

		foreach ( $reviewer_contributor as $entity ) {
			if ( $entity['entity_type'] === 'contributor' ) {
				return $entity['users'];
			}
		}

		return [];
	}

	public function get_reviewers(): ?array {
		$reviewer_contributor = get_field( Tool_Post_Meta::REVIEWER_CONTRIBUTOR );

		if ( ! $reviewer_contributor ) {
			return [];
		}

		foreach ( $reviewer_contributor as $entity ) {
			if ( $entity['entity_type'] === 'reviewer' ) {
				return $entity['users'];
			}
		}

		return [];
	}

	public function get_contributors_content(): ?string {
		$contributors = $this->get_contributors();

		if ( ! $contributors ) {
			return null;
		}

		$contributors_names = '';

		foreach ( $contributors as $key => $contributor ) {
			$contributor_name = $this->get_author_tooltip( $contributor['ID'] );

			if ( $key === count( $contributors ) - 2 ) {
				$contributors_names .= $contributor_name . ' & ';
			} elseif ( $key !== count( $contributors ) - 1 ) {
				$contributors_names .= $contributor_name . ', ';
			} else {
				$contributors_names .= $contributor_name;
			}
		}

		return sprintf(
			'%s %s',
			__( 'With contribution from', 'tribe' ),
			$contributors_names
		);
	}

	public function get_dynamic_cta(): ?ssl_Dynamic_CTA_Model {
		return null;
	}

	public function get_logo_image( string $provider_key = self::X_KEY ): ?Deferred_Component {
		$provider = $this->get_provider( $provider_key );

		if ( ! $provider ) {
			return null;
		}

		return defer_template_part(
			'components/image/image',
			null,
			[
				Image_Controller::IMG_URL      => $provider->get_logo_url(),
				Image_Controller::AS_BG        => false,
				Image_Controller::SRC_SIZE     => 'medium_large',
				Image_Controller::SRCSET_SIZES => [
					'medium',
					'medium_large',
				],
				Image_Controller::CLASSES      => [
					'item-single__logo',
				],
			]
		);
	}

	public function get_content_args(): array {
		$data = [];

		return $data;
	}


	/**
	 * Render the footer component
	 *
	 * Bypasses the get_footer() function to
	 * load our component instead of footer.php
	 *
	 * @return void
	 */
	public function render_footer(): void {
		do_action( 'get_footer', null );
		get_template_part( 'components/document/footer/footer', 'single' );
	}

	public function get_fixed_toc(): ?Deferred_Component {
		return defer_template_part( 'components/table_of_contents/table_of_contents', null, [
			Table_Of_Contents_Controller::STICKY_HEADER => true,
		] );
	}

	public function get_provider( string $key = self::X_KEY ): ?ssl_Provider {
		if ( ! in_array( $key, [ self::X_KEY, self::Y_KEY ] ) ) {
			return null;
		}

		// Ensure XvsY data is loaded before accessing providers
		if ( ! $this->get_ssl_xvsy() ) {
			return null;
		}

		if ( $key === self::X_KEY ) {
			return $this->get_provider_a();
		}

		return $this->get_provider_b();
	}

	public function get_primary_cta(): ?Deferred_Component {
		$primary_cta = $this->get_field( XvsY_Meta::PRIMARY_CTA );

		if ( ! $primary_cta ) {
			return null;
		}

		$provider_a = $this->get_provider_a();
		$provider_b = $this->get_provider_b();

		if ( ! $provider_a || ! $provider_b ) {
			return null;
		}

		return $this->get_cta( self::CTA_PRIMARY, $primary_cta, $provider_a->get_name(), $provider_b->get_name() );
	}

	private function get_cta( $variant, $type, $tool_name_a, $tool_name_b ): ?Deferred_Component {
		$taxonomy_id = null;
		$classes     = [];

		$tool_name_a = sanitize_title( $tool_name_a );
		$tool_name_b = sanitize_title( $tool_name_b );

		if ( ! empty( $ppl_option_category_id = (string) get_field( PPL_Options_Meta::CATEGORY_ID, get_the_ID() ) ) ) {
			$taxonomy_id = $ppl_option_category_id;
		} else {
			$taxonomy_id = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_CATEGORY_ID_DEFAULT, 'option' );
		}

		if ( empty( $taxonomy_id ) ) {
			return null;
		}

		$classes = [
			'item-single__cta-link',
			'a-btn',
		];

		$ppl_url = '#modal-id-ppl-form||category-id-' . $taxonomy_id . '||post-id-' . get_the_ID();

		if ( $type === XvsY_Meta::CTA_OPTION_GET_CUSTOM_QUOTE_UTM ) {
			$ppl_url .= '||utm-provider-' . $tool_name_a . '--provider-' . $tool_name_b;

			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Get Custom Quote', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );
		} elseif ( $type === XvsY_Meta::CTA_OPTION_GET_EXPERT_ADVICE_NO_UTM ) {
			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Get Expert Advice', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );

		} elseif ( $type === XvsY_Meta::CTA_OPTION_BOOK_DEMO_UTM ) {
			$ppl_url .= '||utm-provider-' . $tool_name_a . '--provider-' . $tool_name_b;

			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Book Demo', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );

		} else {
			return null;
		}
	}
}
