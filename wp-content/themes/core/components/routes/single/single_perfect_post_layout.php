<?php
declare( strict_types=1 );

use Tribe\Project\Object_Meta\Post_Settings_Meta;
use Tribe\Project\Templates\Components\post_info\Post_Info_Controller;
use Tribe\Project\Templates\Components\related_posts\Related_Posts_Controller;
use Tribe\Project\Templates\Components\routes\single\Single_Perfect_Post_Layout_Controller;

$c = Single_Perfect_Post_Layout_Controller::factory();

$c->render_header();

$post_info_additional_styles = '';
$post_meta_additional_styles = '';
$blocks                      = $c->convert_enclosing_shortcode_blocks_to_html_blocks( parse_blocks( get_the_content() ) );
$has_toc_inline              = $c->has_a_block_of_type( $blocks, 'acf/tableofcontents' );

if ( empty( $c->get_image_args() ) ) {
	$post_info_additional_styles = ' item-single__post-info--no-featured-image';
}

if ( $c->hide_author() && $c->hide_date() ) {
	$post_info_additional_styles .= ' item-single__post-info--no-post-info';
}

if ( ! empty( $c->get_subtitle_args() ) ) {
	$post_meta_additional_styles .= 'item-single__post-meta--with-subtitle';
}
?>

	<main id="main-content">

		<?php get_template_part( 'components/progress_bar/progress-bar' ); ?>

		<article class="item-single item-single--post item-single--perfect-post-layout" data-js="item-single">

			<header class="l-container">

				<div class="item-single__subheader-wrapper">

					<?php get_template_part( 'components/post_floating_bar/post-floating-bar' ); ?>

					<div class="item-single__post-meta <?php echo $post_meta_additional_styles; ?>">

						<div class="item-single__category-meta">
							<?php if ( $c->get_primary_category_link_args() ) { ?>
								<div class="item-single__category-link">
									<a href="<?php echo $c->get_primary_category_link_args()->link; ?>" class="">
										<?php echo $c->get_primary_category_link_args()->label; ?>
									</a>
								</div>
							<?php } ?>
						</div>

						<?php get_template_part( 'components/text/text', null, $c->get_title_args() ); ?>

						<div class="item-single__post-info-container">
							<div class="item-single__post-info<?php echo $post_info_additional_styles; ?>">
								<?php if ( ! $c->hide_author() || ! $c->hide_date() ) : ?>
									<?php get_template_part( 'components/post_info/post_info', null, [
										'show_author'      => ! $c->hide_author(),
										'show_avatar'      => true,
										'prefix'           => 'By',
										'show_date'        => ! $c->hide_date(),
										'layout'           => Post_Info_Controller::LAYOUT_TWO_LINES,
										'author_args'      => $c->get_author_args( [
											'size'             => 50,
											'show_description' => false,
										] ),
										'multiple_authors' => $c->get_multiple_authors(),
									] ); ?>
								<?php endif; ?>

								<?php if ( $c->get_contributors_content() ) : ?>
									<div class="c-post-info__meta-content c-post-info__meta-content--contributors">
										<span
											class="c-post-info__contributors"><?php echo $c->get_contributors_content(); ?></span>
									</div>
								<?php endif; ?>

								<?php if ( $c->get_header_revision_content() ) : ?>
									<div class="c-post-info__meta-content c-post-info__meta-content--reviewed-by">
										<div class="c-post-info__reviewed-by">
											<div class="c-post-info__reviewed-by-highlighted" data-js="tooltip-trigger">
												<?php get_template_part( 'components/tooltip/tooltip', null, [
													'attrs'   => [
														'itemprop' => 'verifiedLabelExplanation',
													],
													'content' => '<h3>Expert Evidence</h3> <span>This article has undergone a thorough review by an expert in the field, who has carefully examined and evaluated the information presented. Readers can trust that the information provided in this article is reliable and up-to-date.</span>',
												] );
												?>
											</div>

											<?php echo $c->get_header_revision_content(); ?>
										</div>
									</div>
								<?php endif; ?>

								<?php if ( $c->get_header_cta() ) : ?>
									<?php echo $c->get_header_cta(); ?>
								<?php endif; ?>
							</div>
						</div>

						<?php if ( ! empty( $c->get_subtitle_args() ) ) : ?>
							<div class="item-single__post-meta__page-subtitle">
								<?php get_template_part( 'components/text/text', null, $c->get_subtitle_args() ); ?>
							</div>
						<?php endif; ?>

						<?php if ( ! $c->hide_quick_summary() && ! empty( $c->get_excerpt_args() ) ) : ?>
							<div class="item-single__post-meta__excerpt">
								<span
									class="item-single__post-meta__subtitle t-overline"><?php _e( 'QUICK SUMMARY', 'tribe' ); ?></span>
								<?php get_template_part( 'components/text/text', null, $c->get_excerpt_args() ); ?>
							</div>
						<?php endif; ?>

						<?php if ( ! $c->hide_section_jump_links() && $c->get_section_jump_links() && ! $has_toc_inline ) : ?>
							<div class="item-single__post-meta__table-of-contents">
								<span
									class="item-single__post-meta__subtitle t-overline"><?php _e( 'TABLE OF CONTENTS', 'tribe' ); ?></span>
								<?php foreach ( $c->get_section_jump_links() as $link ) :
									if ( ! empty( $link['label'] ) && ! empty( $link['block_id'] ) ) : ?>
										<a class="item-single__post-meta__table-of-contents__link"
										   href="#<?php echo $link['block_id']; ?>"><?php echo $link['label']; ?></a>
										<span class="item-single__post-meta__table-of-contents__separator"></span>
									<?php endif;
								endforeach; ?>
							</div>
						<?php endif; ?>

						<?php if ( $c->get_disclaimer() ) : ?>
							<div class="item-single__post-meta__disclaimer">
								<span
									class="item-single__post-meta__subtitle t-overline"><?php _e( 'DISCLAIMER', 'tribe' ); ?></span>
								<?php echo $c->get_disclaimer(); ?>
							</div>
						<?php endif; ?>

						<?php if ( ! empty( $c->get_image_args() ) ) : ?>
							<div class="item-single__post-meta__featured-image">
								<?php get_template_part( 'components/image/image', null, $c->get_image_args() ); ?>
							</div>
						<?php endif; ?>
					</div>
				</div>
			</header>

			<div class="l-container">
				<div class="item-single__content-container">
					<div class="item-single__content s-sink t-sink l-sink">
						<?php
						if ( wp_is_mobile() ) {
							$c->render_ad( Post_Settings_Meta::AD_GROUP_POST_HEADER, 'header' );
						}

						$average_blocks_count                          = ceil( count( $blocks ) / 2 );
						$show_in_content_ad_automatically              = wp_is_mobile() && ! $c->hide_content_ads();
						$show_in_content_newsletter_automatically      = ! $c->hide_content_newsletters();
						$show_in_content_recommendations_automatically = ! $c->hide_content_recommendations();
						$show_key_takeaways_automatically              = ! $c->hide_key_takeaways();
						$show_in_content_ppl_inline                    = true;
						$show_in_content_ppl_ad                        = true;
						$ad_rendered                                   = false;
						$inline_newsletter_rendered                    = false;
						$inline_recommendations_rendered               = false;
						$show_in_content_ppl_ad_rendered               = false;
						$show_in_content_ppl_inline_rendered           = false;
						$count_rendered_headings                       = 0;
						$average_headings_count                        = 0;
						$quarters_headings_count                       = 0;
						$half_quarter                                  = 0;
						$counting_paragraphs                           = 0;
						$counting_paragraphs_ad_position               = $c->ppl_ad_position();
						$ppl_focused_content                           = $c->is_ppl_focused_content();
						$ppl_is_active                                 = $c->ppl_is_active();

						if ( $c->has_a_block_of_type( $blocks, 'acf/adslot' ) ) {
							$show_in_content_ad_automatically = false;
						}

						if ( $c->has_a_block_of_type( $blocks, 'acf/inlinenewsletter' ) ) {
							$show_in_content_newsletter_automatically = false;
						}

						if ( $c->has_a_block_of_type( $blocks, 'acf/inlinerecommendations' ) ) {
							$show_in_content_recommendations_automatically = false;
						}

						if ( $c->has_a_block_of_type( $blocks, 'acf/keytakeaways' ) ) {
							$show_key_takeaways_automatically = false;
						}

						if ( ! $c->ppl_is_active() || ! $c->ppl_automated_inline_is_active() ) {
							$show_in_content_ppl_inline = false;
						} else {
							if ( $c->has_a_block_of_type( $blocks, 'acf/pplinline' ) ) {
								$show_in_content_ppl_inline = false;
							}
						}

						if ( ! $c->ppl_is_active() || ! $c->ppl_automated_ad_is_active() ) {
							$show_in_content_ppl_ad = false;
						} else {
							if ( $c->has_a_block_of_type( $blocks, 'acf/pplad' ) ) {
								$show_in_content_ppl_ad = false;
							}
						}

						// If there is not an ad slot block, show the ad automatically, based on the rule
						// 0-1 headings, ad in middle of content
						// 2 headings, ad before the second heading
						// 3+ headings, ad before the quantity of headings / 2, rounded up

						// If there is not an inline newsletter block, show the newsletter automatically, based on the rule
						// 5+ headings, newsletter before the quantity of headings / 4, rounded up

						// If there is not an inline recommendations block, show recommendations automatically, based on the rule
						// 5+ headings, recommendations before the quantity of headings - (quantity of headings / 4) + (quantity of headings / 4) / 2, rounded up

						$number_of_headings = $c->count_blocks( $blocks, 'core/heading' );

						if ( $show_in_content_ad_automatically || $show_in_content_newsletter_automatically || $show_in_content_recommendations_automatically ) {
							if ( $number_of_headings <= 2 ) {
								$average_headings_count = $number_of_headings;
							} else {
								$average_headings_count  = ceil( $number_of_headings / 2 );
								$quarters_headings_count = ceil( $number_of_headings / 4 );
								$half_quarter            = ceil( $quarters_headings_count / 2 );
							}
						}

						if ( $average_headings_count === $quarters_headings_count ) {
							$show_in_content_newsletter_automatically      = false;
							$show_in_content_recommendations_automatically = false;
						}

						foreach ( $blocks as $key => $block ) {
							$is_paywall_paragraph = $counting_paragraphs === $c->get_paywall_paragraph_limit();

							if ( $is_paywall_paragraph && $paywall_content_start = $c->render_paywall_content_start() ) {
								echo $paywall_content_start;
							}

							if ( $show_key_takeaways_automatically && $key === 0 ) {
								get_template_part( 'components/blocks/key_takeaways/key_takeaways' );
							}

							if ( $c->is_block_name( $block, 'core/heading' ) ) {
								$count_rendered_headings += 1;
							}

							if ( $show_in_content_ad_automatically && ! $ad_rendered ) {
								if ( $average_headings_count <= 1 && $key == $average_blocks_count ) {
									$c->render_ad( Post_Settings_Meta::AD_GROUP_INLINE, 'content' );
									$ad_rendered = true;
								} elseif ( $average_headings_count > 1 && $count_rendered_headings == $average_headings_count ) {
									$c->render_ad( Post_Settings_Meta::AD_GROUP_INLINE, 'content' );
									$ad_rendered = true;
								}
							}

							if ( $quarters_headings_count > 1 ) {
								if ( $show_in_content_newsletter_automatically
									&& ! $inline_newsletter_rendered
									&& $count_rendered_headings == $quarters_headings_count ) {
									if ( $ppl_focused_content && $ppl_is_active ) {
										echo $c->ppl_inline_injection( $ppl_focused_content );
									} else {
										echo $c->ppl_newsletter_injection();
									}

									$inline_newsletter_rendered = true;
								}

								if ( $show_in_content_recommendations_automatically
									&& ! $inline_recommendations_rendered
									&& $count_rendered_headings == $number_of_headings - $quarters_headings_count + $half_quarter ) {
									get_template_part( 'components/blocks/inline_recommendations/inline_recommendations' );
									$inline_recommendations_rendered = true;
								}
							}

							// add the ppl iframe before the last heading
							if ( $show_in_content_ppl_inline && ! $show_in_content_ppl_inline_rendered && ! $ppl_focused_content && $number_of_headings > 0 && $count_rendered_headings === $number_of_headings ) {
								$show_in_content_ppl_inline_rendered = true;
								echo $c->ppl_inline_injection();
							}

							if ( array_key_exists( 'blockName', $block ) && 'core/group' === $block['blockName'] &&
								array_key_exists( 'innerBlocks', $block ) && ! empty( $block['innerBlocks'] ) ) {
								$content = [];

								foreach ( $block['innerBlocks'] as $inner_block ) {
									$render = '';

									if ( array_key_exists( 'blockName', $inner_block ) && 'core/embed' === $inner_block['blockName'] ) {
										$render = apply_filters( 'the_content', render_block( $inner_block ) );
									} else {
										$render = do_shortcode( render_block( $inner_block ) );
									}

									$content[] = [
										'blockName'    => 'core/html',
										'attrs'        => [],
										'innerBlocks'  => [],
										'innerHTML'    => [ $render ],
										'innerContent' => [ $render ],
									];
								}

								unset( $block['innerBlocks'] );

								$block['innerBlocks'] = $content;

								echo render_block( $block );
							} elseif ( array_key_exists( 'blockName', $block ) && 'core/embed' === $block['blockName'] ) {
								echo apply_filters( 'the_content', render_block( $block ) );
							} else {
								echo do_shortcode( render_block( $block ) );
							}

							// Count every paragraph. Used in PPL Ads and Regwalls.
							if ( array_key_exists( 'blockName', $block ) && 'core/paragraph' === $block['blockName'] ) {
								$counting_paragraphs ++;

								// add the ppl ad before the nth paragraph
								if ( $show_in_content_ppl_ad && ! $show_in_content_ppl_ad_rendered && $counting_paragraphs === $counting_paragraphs_ad_position ) {
									$show_in_content_ppl_ad_rendered = true;
									echo $c->ppl_ad_injection();
								}
							}
						}

						if ( $paywall_content_end = $c->render_paywall_content_end() ) {
							echo $paywall_content_end;
						}

						if ( wp_is_mobile() ) {
							$c->render_ad( Post_Settings_Meta::AD_GROUP_FOOTER, 'footer' );
						}
						?>

						<?php if ( ! $c->hide_author() ) : ?>
							<footer class="item-single__footer l-sink">
								<?php get_template_part( 'components/author/author', null, $c->get_author_args( [
									'show_link_name'   => true,
									'size'             => 120,
									'show_description' => true,
								] ) ); ?>

								<?php echo $c->get_reviewer_as_coauthor(); ?>
								<?php echo $c->get_multiauthor(); ?>
							</footer>
						<?php endif; ?>
					</div>
					<?php if ( ! wp_is_mobile() ) : ?>
						<?php get_template_part( 'components/sidebar_post/sidebar_post', null, [] ); ?>
					<?php endif; ?>
				</div>
			</div>
		</article>

		<?php if ( ! $c->hide_bottom_recommendations() && $related_posts = Related_Posts_Controller::get_primary_category_posts() ) { ?>
			<?php get_template_part( 'components/related_posts/related_posts', null, $related_posts ); ?>
		<?php } ?>
	</main>

<?php

if ( $c->ppl_is_active() && $c->ppl_automated_exit_intent_is_active() ) {
	echo $c->ppl_exit_intent_injection();
}

$c->render_sidebar();
$c->render_footer();
