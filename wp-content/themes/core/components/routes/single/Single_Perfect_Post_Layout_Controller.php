<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\routes\single;

use Tribe\Project\Integrations\Adsanity\Tribe_Adsanity;
use Tribe\Project\Integrations\Crozdesk\Crozdesk_Category;
use Tribe\Project\Multisite\Publications;
use Tribe\Project\Object_Meta\Download_Settings_Meta;
use Tribe\Project\Object_Meta\Media_Settings_Meta;
use Tribe\Project\Object_Meta\Member_Stream_Settings_Meta;
use Tribe\Project\Object_Meta\Post_Meta;
use Tribe\Project\Object_Meta\Post_Settings_Meta;
use Tribe\Project\Object_Meta\PPL_Options_Meta;
use Tribe\Project\Object_Meta\PPL_Settings;
use Tribe\Project\Object_Meta\Tool_Post_Settings_Meta;
use Tribe\Project\Object_Meta\User_Meta;
use Tribe\Project\Post_Types\Download\Download;
use Tribe\Project\Post_Types\Media\Media;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Post_Types\Post\Post;
use Tribe\Project\Post_Types\Tool_Post\Tool_Post;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Templates\Components\breadcrumbs\Breadcrumbs_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\sidebar\Sidebar_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\Traits\Authors_Links;
use Tribe\Project\Templates\Components\Traits\Excerpt;
use Tribe\Project\Templates\Components\Traits\Page_Title;
use Tribe\Project\Templates\Components\Traits\Tag_Loader;
use Tribe\Project\Templates\Components\Traits\Handles_MemberPress_Permissions;
use Tribe\Project\Templates\Components\Traits\Handles_Enclosing_Shortcodes;
use Tribe\Project\Templates\Models\Breadcrumb;
use Tribe\Project\Theme\Config\Image_Sizes;
use Tribe\Project\Templates\Components\Traits\With_Get_Current_Url;
use Tribe\Project\Templates\Components\blocks\ppl_inline\PPL_Inline_Block_Controller;
use WP_Term;

class Single_Perfect_Post_Layout_Controller extends Abstract_Controller {
	use Tag_Loader;
	use Page_Title;
	use Excerpt;
	use Authors_Links;
	use Handles_MemberPress_Permissions;
	use Handles_Enclosing_Shortcodes;
	use With_Get_Current_Url;

	public const CHARACTERS_PER_MINUTE = 200;

	/**
	 * @var int|string
	 */
	private $sidebar_id = '';

	/**
	 * Render the header component
	 *
	 * Bypasses the get_header() function to
	 * load our component instead of header.php
	 *
	 * @return void
	 */
	public function render_header(): void {
		do_action( 'get_header', null );
		get_template_part( 'components/document/header/header', 'single' );
	}

	/**
	 * Render the sidebar component
	 *
	 * Bypasses the get_sidebar() function to
	 * load our component instead of sidebar.php
	 *
	 * @return void
	 */
	public function render_sidebar(): void {
		do_action( 'get_sidebar', null );
		get_template_part(
			'components/sidebar/sidebar',
			'single',
			[ Sidebar_Controller::SIDEBAR_ID => $this->sidebar_id ]
		);
	}

	/**
	 * Render the footer component
	 *
	 * Bypasses the get_footer() function to
	 * load our component instead of footer.php
	 *
	 * @return void
	 */
	public function render_footer(): void {
		do_action( 'get_footer', null );
		get_template_part( 'components/document/footer/footer', 'single' );
	}

	public function render_breadcrumbs(): void {
		//TODO: let's make this get_breadcrumb_args() and render in template
		get_template_part(
			'components/breadcrumbs/breadcrumbs',
			'single',
			[ Breadcrumbs_Controller::BREADCRUMBS => $this->get_breadcrumbs() ]
		);
	}

	/**
	 * @return Breadcrumb[]
	 */
	protected function get_breadcrumbs(): array {
		$page = get_the_ID();
		$url  = $page ? get_permalink( $page ) : home_url();

		return [
			new Breadcrumb( $url, get_the_title( $page ) ),
		];
	}

	public function get_image_args() {
		if ( ! has_post_thumbnail() ) {
			return [];
		}

		if ( wp_is_mobile() ) {
			return [
				Image_Controller::IMG_ID       => (int) get_post_thumbnail_id(),
				Image_Controller::USE_LAZYLOAD => false,
				Image_Controller::IMG_CLASSES  => [ 'post-featured-image' ],
				Image_Controller::ATTRS        => [
					'fetchpriority' => 'high',
				],
				Image_Controller::SRCSET_SIZES => [
					Image_Sizes::SQUARE_SMALL,
					Image_Sizes::SQUARE_MEDIUM,
					Image_Sizes::SQUARE_LARGE,
				],
			];
		}

		return [
			Image_Controller::IMG_ID       => (int) get_post_thumbnail_id(),
			Image_Controller::USE_LAZYLOAD => false,
			Image_Controller::IMG_CLASSES  => [ 'post-featured-image' ],
			Image_Controller::SRC_SIZE     => Image_Sizes::SIXTEEN_NINE_GRID,
			Image_Controller::ATTRS        => [
				'fetchpriority' => 'high',
			],
		];
	}

	public function get_title_args(): array {
		if ( empty( $this->get_page_title() ) ) {
			return [];
		}

		return [
			Text_Controller::TAG     => 'h1',
			Text_Controller::CLASSES => [ 'page-title', 'h1' ],
			Text_Controller::CONTENT => $this->get_page_title(),
		];
	}

	public function get_subtitle_args(): array {
		if ( empty( $this->get_field( Post_Meta::SUBTITLE ) ) ) {
			return [];
		}

		return [
			Text_Controller::TAG     => 'span',
			Text_Controller::CLASSES => [ 'page-subtitle' ],
			Text_Controller::CONTENT => $this->get_field( Post_Meta::SUBTITLE ),
		];
	}

	public function get_excerpt_args(): array {
		if ( empty( $this->get_excerpt() ) ) {
			return [];
		}

		return [
			Text_Controller::TAG     => 'p',
			Text_Controller::CONTENT => $this->get_excerpt(),
		];
	}

	public function get_primary_category(): ?WP_Term {
		if ( ! $id = get_the_ID() ) {
			return null;
		}

		$categories = get_the_terms( $id, 'category' );

		if ( function_exists( 'yoast_get_primary_term' ) ) {
			if ( $primary_category_id = yoast_get_primary_term_id( 'category', $id ) ) {
				$categories = [ get_term( $primary_category_id, 'category' ) ];
			}
		}

		return ( is_array( $categories ) && count( $categories ) >= 1 ) ? $categories[0] : null;
	}

	public function get_primary_category_link_args(): ?object {
		if ( $category = $this->get_primary_category() ) {
			return (object) [
				'link'  => get_term_link( $category ),
				'label' => $category->name,
			];
		}

		return null;
	}

	private function get_default_author_description( $id ): string {
		return Author_Controller::get_the_author_meta( 'user_description', (int) $id );
	}

	private function get_author_description( $id ): string {
		$descriptions = get_field( User_Meta::DESCRIPTION, 'user_' . (int) $id );

		if ( ! $descriptions ) {
			return $this->get_default_author_description( $id );
		}

		$description = array_filter(
			$descriptions,
			function ( $description ) {
				if ( function_exists( 'pll_current_language' ) ) {
					return
						(int) $description[ User_Meta::DESCRIPTION_SITE ] === get_current_blog_id() &&
						$description[ User_Meta::DESCRIPTION_LANGUAGE ] === pll_current_language();
				}

				return
					(int) $description[ User_Meta::DESCRIPTION_SITE ] === get_current_blog_id() &&
					$description[ User_Meta::DESCRIPTION_LANGUAGE ] === User_Meta::DEFAULT_LANGUAGE;
			}
		);

		if ( ! $description ) {
			return $this->get_default_author_description( $id );
		}

		return '<p>' . wp_kses(
			current( $description )[ User_Meta::DESCRIPTION_CONTENT ],
			[
				'a'      => [
					'href'  => [],
					'title' => [],
				],
				'br'     => [],
				'em'     => [],
				'strong' => [],
			]
		) . '</p>';
	}

	public function get_author_args( array $params = [] ): array {
		$id = ! empty( $params['id'] ) ? $params['id'] : get_post_field( 'post_author', get_the_ID() );

		return [
			Author_Controller::AUTHOR_NAME      => Author_Controller::get_author_display_name( $id ),
			Author_Controller::AUTHOR_ID        => $id,
			Author_Controller::AVATAR_SIZE      => $params['size'] ?: 50,
			Author_Controller::SHOW_DESCRIPTION => $params['show_description'] ?: false,
			Author_Controller::AUTHOR_ABOUT     => $this->get_author_description( $id ),
			Author_Controller::SHOW_LINK_NAME   => array_key_exists( 'show_link_name', $params ) ? $params['show_link_name'] : false,
			Author_Controller::SHOW_LINKS_LIST  => $this->get_author_links( (int) $id ),
		];
	}

	protected function get_field( string $field ) {
		$id = get_the_ID();

		return $id ? get_field( $field, $id ) : false;
	}

	public function hide_author(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_AUTHOR ) ?? false;
	}

	public function hide_date(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_DATE ) ?? false;
	}

	public function sponsored_content(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_ADS ) ?? false;
	}

	public function hide_content_newsletters(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_CONTENT_NEWSLETTERS ) ?? false;
	}

	public function hide_content_recommendations(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_CONTENT_RECOMMENDATIONS ) ?? false;
	}

	/**
	 * Get the disclaimer and only return if we are hiding sponsored content
	 *
	 * @return string
	 */
	public function get_disclaimer(): string {
		if ( ! $this->sponsored_content() ) {
			return '';
		}

		return $this->get_field( Post_Meta::DISCLAIMER ) ?? '';
	}

	public function hide_content_ads(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_CONTENT_ADS ) ?? false;
	}

	/**
	 * @return bool
	 */
	public function hide_quick_summary(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_QUICK_SUMMARY ) ?? false;
	}

	public function hide_section_jump_links(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_SECTION_JUMP_LINKS ) ?? false;
	}

	public function get_section_jump_links(): array {
		$fields = $this->get_field( Post_Meta::SECTION_JUMP_LINKS );

		if ( ! is_array( $fields ) ) {
			return [];
		}

		return ( ! empty( $fields[0][ Post_Meta::BLOCK_ID ] ) ) ? $fields : [];
	}

	public function get_sidebar_size(): int {
		return $this->get_field( Post_Meta::SIDEBAR_SIZE ) ? (int) $this->get_field( Post_Meta::SIDEBAR_SIZE ) : Post_Meta::SIDEBAR_SIZE_DEFAULT;
	}

	public function get_replace_main_ad_for_three_pack(): bool {
		return $this->get_field( Post_Meta::REPLACE_MAIN_AD_FOR_THREE_PACK ) === null ? false : $this->get_field( Post_Meta::REPLACE_MAIN_AD_FOR_THREE_PACK );
	}

	public function get_post_type_archive(): ?string {
		$id        = get_the_ID();
		$post_type = get_post_type( $id );

		return get_post_type_archive_link( $post_type );
	}

	public function has_adsanity_ad_running( $ad_group ): bool {
		if ( $this->sponsored_content() ) {
			return false;
		}

		if ( $ad_group ) {
			$args = [
				Tribe_Adsanity::GROUP_IDS   => [ $ad_group ],
				Tribe_Adsanity::NUM_COLUMNS => 1,
				Tribe_Adsanity::NUM_ADS     => 1,
			];

			return Tribe_Adsanity::adsanity_has_active_ad_in_ad_group( $args );
		} else {
			return false;
		}
	}

	public function render_ad( $post_ad_group, $slot_id = '', bool $return = false ) {
		if ( ! $this->sponsored_content() ) {
			$id        = get_the_ID();
			$post_type = get_post_type( $id );
			$layout    = 'inline';

			if ( $post_ad_group === Post_Settings_Meta::AD_GROUP_FOOTER || $post_ad_group === Media_Settings_Meta::AD_GROUP_FOOTER || $post_ad_group === Tool_Post_Settings_Meta::AD_GROUP_FOOTER || $post_ad_group === Member_Stream_Settings_Meta::AD_GROUP_FOOTER ) {
				$layout = 'footer';
			}

			if ( (int) $post_ad_group > 0 ) {
				$ad_group = $post_ad_group;
			} else {
				switch ( $post_type ) {
					case Post::NAME:
						$ad_group = get_field( Post_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					case Media::NAME:
						$ad_group = get_field( Media_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					case Tool_Post::NAME:
						$ad_group = get_field( Tool_Post_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					case Member_Stream::NAME:
						$ad_group = get_field( Member_Stream_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					case Download::NAME:
						$ad_group = get_field( Download_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					default:
						$ad_group = get_field( Post_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
				}
			}

			if ( $ad_group ) {
				$args = [
					Tribe_Adsanity::GROUP_IDS   => [ $ad_group ],
					Tribe_Adsanity::NUM_COLUMNS => 1,
					Tribe_Adsanity::SLOT_ID     => $slot_id ?? null,
					Tribe_Adsanity::RETURN      => $return,
					Tribe_Adsanity::NUM_ADS     => 1,
				];

				return Tribe_Adsanity::adsanity_show_ad_group( $args, $layout );
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	public function get_contributors_content(): ?string {
		$contributors = $this->get_contributors();

		if ( ! $contributors ) {
			return null;
		}

		$contributors_names = '';

		foreach ( $contributors as $key => $contributor ) {
			$contributor_name = $this->get_author_tooltip( $contributor['ID'] );

			if ( $key === count( $contributors ) - 2 ) {
				$contributors_names .= $contributor_name . ' & ';
			} elseif ( $key !== count( $contributors ) - 1 ) {
				$contributors_names .= $contributor_name . ', ';
			} else {
				$contributors_names .= $contributor_name;
			}
		}

		return sprintf(
			'%s %s',
			__( 'With contribution from', 'tribe' ),
			$contributors_names
		);
	}

	public function get_header_revision_content(): ?string {
		$reviewers   = $this->get_reviewers();
		$reviewed_on = get_field( Post_Meta::REVIEWED_ON );

		if ( ! $reviewers ) {
			return null;
		}

		$reviewers_names = '';

		foreach ( $reviewers as $key => $reviewer ) {
			$reviewer_name = $this->get_author_tooltip( $reviewer['ID'] );

			if ( $key === count( $reviewers ) - 2 ) {
				$reviewers_names .= $reviewer_name . ' & ';
			} elseif ( $key !== count( $reviewers ) - 1 ) {
				$reviewers_names .= $reviewer_name . ', ';
			} else {
				$reviewers_names .= $reviewer_name;
			}
		}

		if ( $reviewed_on ) {
			$content = sprintf(
			/* translators: %s Name of a user, and %s is the reviewed date */
				'%s %s %s %s',
				__( 'Reviewed by', 'tribe' ),
				$reviewers_names,
				__( 'on', 'tribe' ),
				date( 'M j, Y', strtotime( $reviewed_on ) )
			);
		} else {
			$content = sprintf(
				'%s %s',
				__( 'Reviewed by', 'tribe' ),
				$reviewers_names
			);
		}

		return $content;
	}

	public function get_author_tooltip( int $id ): ?string {
		if ( ! $id ) {
			return '';
		}

		$author_description_content = ( new Author_Controller( [ Author_Controller::AUTHOR_ID => $id ] ) )->get_author_description();

		if ( ! preg_match( '/^<p>/', $author_description_content ) ) {
			$author_description_content = '<p>' . $author_description_content . '</p>';
		}


		$author_description = ! empty( $author_description_content ) ? '<div class="c-tooltip__description">' . $author_description_content . '</div>' : '';
		$author_name        = Author_Controller::get_author_display_name( $id );
		$first_name         = Author_Controller::get_the_author_meta( 'first_name', $id );
		$author_title       = Author_Controller::get_the_author_meta( 'user_title', $id );
		$author_link        = defer_template_part( 'components/link/link', null, [
			Link_Controller::URL     => get_author_posts_url( $id ),
			Link_Controller::CONTENT => __( 'More about ' . $first_name, 'tribe' ),
			Link_Controller::TARGET  => '_self',
			Link_Controller::CLASSES => [
				'a-cta',
			],
		] );
		$author_avatar      = defer_template_part( 'components/image/image', null, [
			Image_Controller::IMG_URL      => esc_url( Author_Controller::get_author_avatar_from_id( $id, 64 ) ),
			Image_Controller::LINK_URL     => esc_url( get_author_posts_url( $id ) ),
			Image_Controller::LINK_ID      => 'item-single__author-image',
			Image_Controller::AS_BG        => true,
			Image_Controller::USE_LAZYLOAD => true,
			Image_Controller::CLASSES      => [ 'item-single__author-image' ],
			Image_Controller::IMG_ALT_TEXT => esc_attr( $author_name ),
			Image_Controller::IMG_CLASSES  => [ 'item-single__author-image-img' ],
		] );

		$tooltip        = defer_template_part( 'components/tooltip/tooltip', null, [
			'attrs'   => [
				'itemprop' => 'reviewerInformation',
			],
			'content' => '<div class="c-tooltip__author-info-wrapper">' . $author_avatar . '<div class="c-tooltip__author-info-wrapper--details"><span>' . $author_name . '</span> <span>' . $author_title . '</span></div></div>' . $author_description . $author_link,
		] );
		$author_tooltip = '<div class="c-post-info__author-data" data-js="tooltip-trigger"><span class="c-post-info__author-name">' . $author_name . '</span>' . $tooltip . '</div>';

		return $author_tooltip;
	}

	public function get_contributors(): ?array {
		$reviewer_contributor = get_field( Post_Meta::REVIEWER_CONTRIBUTOR );

		if ( ! $reviewer_contributor ) {
			return [];
		}

		foreach ( $reviewer_contributor as $entity ) {
			if ( ! $entity['users'] ) {
				continue;
			}

			if ( $entity['entity_type'] === 'contributor' ) {
				return $entity['users'];
			}
		}

		return [];
	}

	public function get_reviewers(): ?array {
		$reviewer_contributor = get_field( Post_Meta::REVIEWER_CONTRIBUTOR );

		if ( ! $reviewer_contributor ) {
			return [];
		}

		foreach ( $reviewer_contributor as $entity ) {
			if ( ! $entity['users'] ) {
				continue;
			}

			if ( $entity['entity_type'] === 'reviewer' ) {
				return $entity['users'];
			}
		}

		return [];
	}

	public function get_header_cta(): ?Deferred_Component {
		$header_cta = $this->get_global_header_cta_translation();

		if ( ! $header_cta ) {
			return null;
		}

		if ( $this->is_ppl_focused_content() && $this->ppl_is_active() ) {
			$cta_text = $header_cta[ Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_TEXT ] ?? '';
			$cta_link = $header_cta[ Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_LINK ] ?? '';
		} else {
			$cta_text = $header_cta[ Post_Settings_Meta::HEADER_CTA_TEXT ] ?? '';
			$cta_link = $header_cta[ Post_Settings_Meta::HEADER_CTA_LINK ] ?? '';
		}

		$link = '';

		if ( ! empty( $cta_link ) ) {
			$cta = wp_parse_args( $cta_link, [
				'title'  => '',
				'url'    => '',
				'target' => '',
			] );

			$link = defer_template_part( 'components/link/link', null, [
				Link_Controller::ID      => 'perfect-post-info__subscribe--link',
				Link_Controller::URL     => $cta['url'],
				Link_Controller::CONTENT => $cta['title'] ?: $cta['url'],
				Link_Controller::TARGET  => $cta['target'],
				Link_Controller::CLASSES => [
					'c-post-info__subscribe--link',
				],
			] );
		}

		$content = defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'span',
			Container_Controller::CLASSES => [
				'c-post-info__subscribe',
			],
			Container_Controller::CONTENT => ! empty( $cta_text ) ? $cta_text . $link : '',
		] );

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				'c-post-info__meta-content',
				'c-post-info__meta-content--subscribe',
			],
			Container_Controller::CONTENT => $content,
		] );
	}

	private function get_global_header_cta_translation(): ?array {
		$translations = get_field( Post_Settings_Meta::HEADER_CTA, 'option' );

		if ( ! $translations ) {
			return null;
		}

		$prepared_translations = [];

		foreach ( $translations as $translated ) {
			$prepared_translations[ $translated[ Post_Settings_Meta::HEADER_CTA_LANGUAGE ] ] = [
				Post_Settings_Meta::HEADER_CTA_TEXT             => $translated[ Post_Settings_Meta::HEADER_CTA_TEXT ],
				Post_Settings_Meta::HEADER_CTA_LINK             => $translated[ Post_Settings_Meta::HEADER_CTA_LINK ],
				Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_TEXT => $translated[ Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_TEXT ],
				Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_LINK => $translated[ Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_LINK ],
			];
		}

		$lang = Post_Settings_Meta::DEFAULT_LANGUAGE;

		if ( function_exists( 'pll_current_language' ) ) {
			$lang = pll_current_language();
		}

		if ( array_key_exists( $lang, $prepared_translations ) ) {
			$content = $prepared_translations[ $lang ];
		} elseif ( array_key_exists( Post_Settings_Meta::DEFAULT_LANGUAGE, $prepared_translations ) ) {
			$content = $prepared_translations[ Post_Settings_Meta::DEFAULT_LANGUAGE ];
		} else {
			$content = null;
		}

		if ( empty( $content ) ) {
			return null;
		}

		return [
			Post_Settings_Meta::HEADER_CTA_TEXT             => $content[ Post_Settings_Meta::HEADER_CTA_TEXT ],
			Post_Settings_Meta::HEADER_CTA_LINK             => $content[ Post_Settings_Meta::HEADER_CTA_LINK ],
			Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_TEXT => $content[ Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_TEXT ],
			Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_LINK => $content[ Post_Settings_Meta::PPL_FOCUSED_HEADER_CTA_LINK ],
		];
	}

	public function ppl_is_active(): bool {
		return (bool) get_field( PPL_Options_Meta::PPL_IS_ACTIVE, get_the_ID() ) ?? false;
	}

	public function ppl_automated_inline_is_active(): bool {
		return (bool) get_field( PPL_Options_Meta::AUTOMATED_INLINE_IS_ACTIVE, get_the_ID() ) ?? false;
	}

	public function ppl_automated_ad_is_active(): bool {
		return (bool) get_field( PPL_Options_Meta::AUTOMATED_AD_IS_ACTIVE, get_the_ID() ) ?? false;
	}

	public function ppl_automated_exit_intent_is_active(): bool {
		return (bool) get_field( PPL_Options_Meta::AUTOMATED_EXIT_INTENT_IS_ACTIVE, get_the_ID() ) ?? false;
	}

	public function ppl_ad_position(): ?int {
		return (int) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_AD_PARAGRAPHS_COUNT, 'option' ) ?? null;
	}

	public function ppl_inline_injection( bool $ppl_focused_content = false ): ?Deferred_Component {
		return defer_template_part( 'components/blocks/ppl_inline/ppl_inline', null, [
			PPL_Inline_Block_Controller::PPL_FOCUSED => $ppl_focused_content,
		] );
	}

	public function ppl_newsletter_injection(): ?Deferred_Component {
		return defer_template_part( 'components/blocks/inline_newsletter/inline_newsletter', null, [
			'newsletter_tag' => true,
		] );
	}

	public function ppl_ad_injection(): ?Deferred_Component {
		return defer_template_part( 'components/blocks/ppl_ad/ppl_ad' );
	}

	public function ppl_exit_intent_injection(): ?string {
		$id                = get_the_ID();
		$exit_intent_title = '';
		$category_id       = null;
		$utm_medium        = 'exit_intent_iframe';
		$site_abbreviation = (string) get_option( 'bawz_site_abbreviation' ) ?? 'crozdesk';

		if ( $option_title = get_field( PPL_Options_Meta::AUTOMATED_EXIT_INTENT_TITLE, $id ) ) {
			$exit_intent_title = $option_title;
		} elseif ( $default_title = PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_EXIT_INTENT_TITLE_DEFAULT, 'option' ) ) {
			$exit_intent_title = $default_title;
		}

		if ( ! empty( $ppl_option_category_id = (string) get_field( PPL_Options_Meta::CATEGORY_ID, $id ) ) ) {
			$category_id = $ppl_option_category_id;
		} else {
			$category_id = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_CATEGORY_ID_DEFAULT, 'option' );
		}

		$crozdesk_category = Crozdesk_Category::get_crozdesk_category( $category_id );

		if ( ! $crozdesk_category ) {
			return null;
		}

		$relative_path = $crozdesk_category[ Crozdesk_Category::CROZDESK_CATEGORY_RELATIVE_PATH ];

		if ( ! $relative_path ) {
			return null;
		}

		$relative_path = ltrim( $relative_path, '/' );
		$referrer      = $this->get_current_url_referer_param( true, '&' );

		// TODO: Only DPM and PMP have the 4-step form
		if ( Publications::is_pub( [ Publications::DPM, Publications::PMP ] ) ) {
			$iframe_url = "https://crozdesk.com/$relative_path/quote-form/embed-$site_abbreviation-4-steps-v2?utm_medium=$utm_medium{$referrer}&utm_content=" . get_post_field( 'post_name', $id ) . '--embed-4-step-v2';
		} else {
			$iframe_url = "https://crozdesk.com/$relative_path/quote-form/embed-$site_abbreviation?utm_medium=$utm_medium{$referrer}&utm_content=" . get_post_field( 'post_name', $id );
		}

		return <<<JS
		<script type="text/javascript">
			window.pplExitIntent = true;

			document.addEventListener('om.Dtr.init', function(event) {
				event.detail.Dtr.setCustomVariable('bwzPPLTitle', '$exit_intent_title');
				event.detail.Dtr.setCustomVariable('bwzPPLIFrameURL', '$iframe_url');
			});
		</script>
JS;
	}

	public function has_a_block_of_type( $parsed_blocks, $block_name ): bool {
		return array_search( $block_name, array_column( $parsed_blocks, 'blockName' ) ) !== false;
	}

	public function count_blocks( $parsed_blocks, $block_name ): int {
		return count( array_filter( array_column( $parsed_blocks, 'blockName' ), function ( $value ) use ( $block_name ) {
			return $value === $block_name;
		} ) );
	}

	public function is_block_name( $block, $block_name ): bool {
		return array_key_exists( 'blockName', $block ) && $block['blockName'] === $block_name;
	}

	public function get_multiauthor(): ?string {
		$multiauthor = get_field( Post_Meta::MULTIAUTHOR );

		if ( ! $multiauthor ) {
			return '';
		}

		$content = '';

		foreach ( $multiauthor as $authorID ) {
			$user = get_user_by( 'ID', $authorID );

			$content .= get_template_part( 'components/author/author', null, $this->get_author_args( [
				'id'               => $authorID,
				'size'             => 120,
				'show_description' => true,
				'show_link_name'   => true,
			] ) );
		}

		return $content;
	}

	public function get_reviewer_as_coauthor(): ?string {
		$add_reviewer_as_coauthor = get_field( Post_Meta::REVIEWER_AS_COAUTHOR );
		$reviewers                = $this->get_reviewers();

		if ( ! $add_reviewer_as_coauthor || ! $reviewers ) {
			return '';
		}

		$content = '';

		foreach ( $reviewers as $reviewer ) {
			$content .= get_template_part( 'components/author/author', null, $this->get_author_args( [
				'id'               => $reviewer['ID'],
				'link_id'          => 'reviewer_' . $reviewer['ID'],
				'size'             => 120,
				'show_description' => true,
				'show_link_name'   => true,
			] ) );
		}

		return $content;
	}

	public function get_multiple_authors(): array {
		$add_reviewer_as_coauthor = get_field( Post_Meta::REVIEWER_AS_COAUTHOR );
		$reviewers                = $this->get_reviewers();
		$multiauthor              = get_field( Post_Meta::MULTIAUTHOR );

		$authors = [];
		if ( $add_reviewer_as_coauthor && $reviewers ) {
			foreach ( $reviewers as $reviewer ) {
				$authors[] = $reviewer['ID'];
			}
		}

		if ( ! empty( $multiauthor ) ) {
			foreach ( $multiauthor as $authorID ) {
				$authors[] = $authorID;
			}
		}

		return $authors;
	}

	public function hide_key_takeaways(): ?bool {
		return get_field( Post_Meta::HIDE_KEY_TAKEAWAYS );
	}

	/**
	 * @return bool
	 */
	public function hide_bottom_recommendations(): bool {
		return (bool) get_field( Post_Meta::HIDE_BOTTOM_RECOMMENDATIONS );
	}

	/**
	 * @return bool
	 */
	public function is_ppl_focused_content(): bool {
		return (bool) get_field( Post_Meta::PPL_FOCUSED_CONTENT );
	}
}
