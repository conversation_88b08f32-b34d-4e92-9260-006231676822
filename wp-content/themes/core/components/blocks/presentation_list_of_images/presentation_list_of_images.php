<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\blocks\presentation_list_of_images\Presentation_List_of_Images_Block_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = Presentation_List_of_Images_Block_Controller::factory( $args );
?>

<section <?php echo $c->get_classes(); ?> <?php echo $c->get_attrs(); ?>>
	<div <?php echo $c->get_container_classes();?>>
		<?php get_template_part(
			'components/content_block/content_block',
			null,
			$c->get_header_args()
		); ?>
	</div>

	<?php if ( $c->get_cards() ) : ?>
		<?php get_template_part(
			'components/slider/slider',
			null,
			$c->get_mobile_swipe_args(),
		); ?>
	<?php endif; ?>
</section>
