<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\presentation_list_of_images;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Blocks\Types\Disclaimer\Disclaimer as Dislclaimer_Block;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\ad_block\Ad_Block_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\content_block\Content_Block_Controller;
use Tribe\Project\Blocks\Types\Presentation_List_of_Images\Presentation_List_of_Images;
use Tribe\Project\Templates\Components\card\Card_Controller;
use Tribe\Project\Templates\Components\slider\Slider_Controller;
use Tribe\Project\Theme\Config\Image_Sizes;

class Presentation_List_of_Images_Block_Controller extends Abstract_Controller {
	public const CONTAINER_CLASSES = 'container_classes';
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const TITLE             = 'title';
	public const DESCRIPTION       = 'description';
	public const CARDS             = 'cards';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private array  $cards;
	private string $title;
	private string $description;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->cards             = (array) $args[ self::CARDS ];
		$this->title             = (string) $args[ self::TITLE ];
		$this->description       = (string) $args[ self::DESCRIPTION ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::CARDS             => [],
			self::TITLE             => '',
			self::DESCRIPTION       => '',
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [ 'l-container' ],
			self::CLASSES           => [ 'c-block', 'b-presentation-list-of-images' ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return array
	 */
	public function get_header_args(): array {
		if ( empty( $this->title ) && empty( $this->description ) ) {
			return [];
		}

		return [
			Content_Block_Controller::TAG     => 'header',
			Content_Block_Controller::TITLE   => $this->get_title(),
			Content_Block_Controller::CONTENT => $this->get_content(),
			Content_Block_Controller::CLASSES => [
				'c-block__content-block',
				'c-block__header',
				'b-presentation-list-of-images__header',
			],
		];
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_title(): Deferred_Component {
		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => $this->heading_tag,
			Text_Controller::CLASSES => [
				'c-block__title',
				'h3',
			],
			Text_Controller::CONTENT => $this->title ?? '',
		] );
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_content(): Deferred_Component {
		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				'c-block__description',
				'b-presentation-list-of-images__description',
				't-sink',
				's-sink',
			],
			Container_Controller::CONTENT => $this->description ?? '',
		] );
	}

	/**
	 * @return string
	 */
	protected function get_slider_options(): string {
		$args = [
			'keyboard'              => "true",
			'grabCursor'            => "true",
			'pagination'            => "false",
			'allowTouchMove'        => "true",
			'speed'                 => 4500,
			'freeMode'              => [
				'enabled' => true,
			],
			'freeModeMomentum'      => "true",
			'freeModeMomentumRatio' => 0.1,
			'loop'                  => "true",
			'autoplay'              => [
				'disableOnInteraction' => false,
				'delay'                => 0,
			],
			'centeredSlides'        => true,
			'spaceBetween'          => count( $this->get_cards() ) > 1 ? 40 : null,
			'breakpoints'           => [
				'320' => [
					'slidesPerView' => 2,
					'spaceBetween'  => count( $this->get_cards() ) > 1 ? 9 : null,
				],
				'768' => [
					'slidesPerView' => 4,
				],
				'960' => [
					'slidesPerView' => 6,
				],
			],
		];

		return json_encode( $args );
	}

	/**
	 * Get the Slider
	 *
	 * @return array
	 */
	public function get_mobile_swipe_args(): array {
		$slides = [];

		foreach ( $this->get_cards_args() as $card ) {
			if ( $card ) {
				$slides[] = $this->get_card( $card );
			}
		}

		$main_attrs                        = [];
		$main_attrs['data-swiper-options'] = $this->get_slider_options();

		return [
			Slider_Controller::SHOW_PAGINATION => false,
			Slider_Controller::SHOW_ARROWS     => false,
			Slider_Controller::SLIDES          => $slides,
			Slider_Controller::MAIN_ATTRS      => $main_attrs,
			Slider_Controller::CLASSES         => [ 'b-cards__slider' ],
			Slider_Controller::SLIDE_CLASSES   => [ 'b-cards__slide' ],
		];
	}

	public function get_cards_args() {
		$cards = [];
		foreach ( $this->cards as $card ) {
			$uuid = uniqid( 'p-' );

			$card_description = defer_template_part(
				'components/text/text',
				null,
				[
					Text_Controller::TAG     => 'p',
					Text_Controller::CONTENT => $card[ Presentation_List_of_Images::CARDS_DESCRIPTION ],
				]
			);

			$cards[] = [
				Card_Controller::TAG         => 'div',
				Card_Controller::STYLE       => Card_Controller::STYLE_INSIDE,
				Card_Controller::TITLE       => defer_template_part(
					'components/text/text',
					null,
					[
						Text_Controller::TAG     => 'h3',
						Text_Controller::CONTENT => $card[ Presentation_List_of_Images::CARDS_TITLE ],
						// Required for screen reader accessibility, below.
						Text_Controller::ATTRS   => [ 'id' => $uuid . '-title' ],
					]
				),
				Card_Controller::DESCRIPTION => defer_template_part(
					'components/container/container',
					null,
					[
						Container_Controller::CONTENT => $card_description,
						Container_Controller::CLASSES => [ 't-sink', 's-sink' ],
					],
				),
				Card_Controller::IMAGE       => defer_template_part(
					'components/image/image',
					null,
					[
						Image_Controller::IMG_ID   => $card[ Presentation_List_of_Images::CARDS_IMAGE ],
						Image_Controller::CLASSES  => [ 'c-image' ],
						Image_Controller::SRC_SIZE => Image_Sizes::CORE_MOBILE,
					],
				),
			];
		}

		return $cards;
	}

	public function get_cards(): array {
		if ( empty( $this->cards ) ) {
			return [];
		}

		return $this->cards;
	}

	public function get_card( array $card = [] ): Deferred_Component {
		return defer_template_part(
			'components/card/card',
			null,
			$card
		);
	}

}
