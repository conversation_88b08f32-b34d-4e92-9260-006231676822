<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\ppl_inline;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Integrations\Crozdesk\Crozdesk_Category;
use Tribe\Project\Multisite\Publications;
use Tribe\Project\Object_Meta\PPL_Options_Meta;
use Tribe\Project\Object_Meta\PPL_Settings;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\Traits\With_Get_Current_Url;

class PPL_Inline_Block_Controller extends Abstract_Controller {
	use With_Get_Current_Url;

	public const TITLE             = 'title';
	public const DESCRIPTION       = 'description';
	public const HIDE_TITLE        = 'hide_title';
	public const HIDE_DESCRIPTION  = 'hide_description';
	public const CATEGORY_ID       = 'category_id';
	public const CONTAINER_CLASSES = 'container_classes';
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const POST_ID           = 'post_id';
	public const PPL_FOCUSED       = 'ppl_focused';

	public const CLASS_BASE = 'b-ppl-inline';

	private string $title;
	private string $description;
	private bool   $hide_title;
	private bool   $hide_description;
	private string $category_id;
	private string $site_abbreviation;
	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private int    $post_id;
	private string $anchor_id;
	private bool   $ppl_focused;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );
		$id   = array_key_exists( self::POST_ID, $args ) ? $args[ self::POST_ID ] : 0;

		$this->title             = (string) $args[ self::TITLE ];
		$this->description       = (string) $args[ self::DESCRIPTION ];
		$this->hide_title        = (bool) $args[ self::HIDE_TITLE ];
		$this->hide_description  = (bool) $args[ self::HIDE_DESCRIPTION ];
		$this->category_id       = array_key_exists( self::CATEGORY_ID, $args ) ? (string) $args[ self::CATEGORY_ID ] : '';
		$this->site_abbreviation = (string) get_option( 'bawz_site_abbreviation' ) ?? 'crozdesk';
		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->post_id           = (int) $id;
		$post_slug               = sanitize_title( get_the_title( $id ) );
		$this->anchor_id         = "need-expert-help_$post_slug";
		$this->ppl_focused       = (bool) $args[ self::PPL_FOCUSED ];

		if ( empty( $this->title ) ) {
			if ( ! empty( $ppl_option_title = (string) get_field( PPL_Options_Meta::AUTOMATED_INLINE_TITLE, $id ) ) ) {
				$this->title = $ppl_option_title;
			} else {
				$this->title = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_INLINE_TITLE_DEFAULT, 'option' );
			}
		}

		if ( empty( $this->description ) ) {
			if ( ! empty( $ppl_option_description = (string) get_field( PPL_Options_Meta::AUTOMATED_INLINE_DESCRIPTION, $id ) ) ) {
				$this->description = $ppl_option_description;
			} else {
				$this->description = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_INLINE_DESCRIPTION_DEFAULT, 'option' );
			}
		}

		if ( empty( $this->category_id ) ) {
			if ( ! empty( $ppl_option_category_id = (string) get_field( PPL_Options_Meta::CATEGORY_ID, $id ) ) ) {
				$this->category_id = $ppl_option_category_id;
			} else {
				$this->category_id = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_CATEGORY_ID_DEFAULT, 'option' );
			}
		}

		$crozdesk_category = Crozdesk_Category::get_crozdesk_category( $this->category_id );

		if ( ! empty( $crozdesk_category ) ) {
			$this->title       = str_replace( '{{category_name}}', $crozdesk_category[ Crozdesk_Category::CROZDESK_CATEGORY_NAME ], $this->title );
			$this->description = str_replace( '{{category_name}}', $crozdesk_category[ Crozdesk_Category::CROZDESK_CATEGORY_NAME ], $this->description );
		}
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::TITLE             => '',
			self::DESCRIPTION       => '',
			self::HIDE_TITLE        => false,
			self::HIDE_DESCRIPTION  => false,
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::PPL_FOCUSED       => false,
		];
	}

	public function append_class_name( $class ): string {
		return self::CLASS_BASE . '__' . $class;
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [ self::CLASS_BASE ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		if ( $this->is_ppl_focused() ) {
			$this->classes[] = self::CLASS_BASE . '__' . 'ppl-focused';
		}

		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		$this->attrs['id'] = $this->anchor_id;

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		if ( $this->is_ppl_focused() ) {
			$this->container_classes = [ self::CLASS_BASE . '__' . 'spotlight' ];
		}

		return Markup_Utils::class_attribute( $this->container_classes );
	}

	public function ppl_is_active(): bool {
		return (bool) get_field( PPL_Options_Meta::PPL_IS_ACTIVE, $this->post_id ) ?? false;
	}

	public function get_category_id(): string {
		return $this->category_id;
	}

	public function hide_title(): bool {
		if ( $this->hide_title ) {
			return true;
		}

		return (bool) get_field( PPL_Options_Meta::HIDE_TITLE_PPL_INLINE, $this->post_id ) ?? false;
	}

	public function get_title(): Deferred_Component {
		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h2',
			Text_Controller::CLASSES => [
				'c-block__title',
				$this->append_class_name( 'title' ),
			],
			Text_Controller::CONTENT => $this->title ?? '',
		] );
	}

	public function hide_description(): bool {
		if ( $this->hide_description ) {
			return true;
		}

		return (bool) get_field( PPL_Options_Meta::HIDE_DESCRIPTION_PPL_INLINE, $this->post_id ) ?? false;
	}

	public function get_description(): Deferred_Component {
		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				'c-block__description',
				$this->append_class_name( 'description' ),
				't-sink',
				's-sink',
			],
			Container_Controller::CONTENT => $this->description ?? '',
		] );
	}

	public function get_ppl_iframe_url( $utm_medium ): ?string {
		if ( ! $crozdesk_category = Crozdesk_Category::get_crozdesk_category( $this->category_id ) ) {
			return null;
		}

		$relative_path = $crozdesk_category[ Crozdesk_Category::CROZDESK_CATEGORY_RELATIVE_PATH ];

		if ( ! $relative_path ) {
			return null;
		}

		$relative_path = ltrim( $relative_path, '/' );
		$referrer      = $this->get_current_url_referer_param( true, '&' );

		// TODO: Only DPM and PMP have the 4-step form
		if ( Publications::is_pub( [ Publications::DPM, Publications::PMP ] ) ) {
			return "https://crozdesk.com/$relative_path/quote-form/embed-$this->site_abbreviation-4-steps-v2?utm_medium=$utm_medium{$referrer}&utm_content=" . get_post_field( 'post_name', $this->post_id ) . '--embed-4-step-v2';
		}

		return "https://crozdesk.com/$relative_path/quote-form/embed-$this->site_abbreviation?utm_medium=$utm_medium{$referrer}&utm_content=" . get_post_field( 'post_name', $this->post_id );
	}

	public function is_ppl_focused(): bool {
		return (bool) $this->ppl_focused;
	}
}
