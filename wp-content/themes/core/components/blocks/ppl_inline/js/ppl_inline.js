/** -----------------------------------------------------------------------------
 *
 * Component: Block: Inline Newsletter
 *
 * Script to enable/disale the swiper for card grids with 3 cards
 *
 * ----------------------------------------------------------------------------- */

import * as tools from 'utils/tools';
import { on } from 'utils/events';

const el = {
	ppl_inline_focused: null,
	ppl_inline_height_half: 0,
	screen_height: window.innerHeight,
	screen_height_half: window.innerHeight * 0.5,
	maximum_opacity: 0.8,
};

// Store Component State
const component = {
	viewport: window.innerWidth >= 1200 ? 'desktop' : 'mobile',
};

/**
 * Get Viewport
 * @returns {string}
 */
const getViewPort = () => {
	return window.innerWidth >= 1200 ? 'desktop' : 'mobile';
};

/**
 * @function setHeightOfSpotlight
 * @description Set the height of the container of the spotlight based on the content.
 */
function setHeightOfSpotlight() {
	const newsletter_container = tools.getNodes( '.item-single__content .b-ppl-inline__spotlight', true, document, true )[ 0 ];
	const iframe = tools.getNodes( '.item-single__content .b-ppl-inline__spotlight iframe', true, document, true )[ 0 ];

	if ( newsletter_container ) {
		newsletter_container.style.height = iframe.offsetHeight + 'px';
		el.ppl_inline_focused.style.setProperty( '--container-height', newsletter_container.offsetHeight + 'px' );
	}
}

/**
 * @function handleSpotlightScroll
 * @description Load the Spotlight behaviour.
 */
const handleSpotlightScroll = () => {
	const scrollingPosition = window.pageYOffset || document.documentElement.scrollTop;

	setHeightOfSpotlight();

	if ( scrollingPosition > el.ppl_inline_focused.offsetTop - el.screen_height_half - el.ppl_inline_height_half && scrollingPosition < el.ppl_inline_focused.offsetTop ) {
		let number = scrollingPosition - el.ppl_inline_focused.offsetTop + el.ppl_inline_height_half;

		number = Math.max( el.screen_height_half * -1, Math.min( el.ppl_inline_height_half, number ) );
		let opacity = 1 - Math.abs( number ) / ( el.ppl_inline_height_half );

		if ( opacity >= el.maximum_opacity ) {
			opacity = el.maximum_opacity;
		}

		tools.addClass( el.ppl_inline_focused, 'b-ppl-inline--with-spotlight' );
		el.ppl_inline_focused.style.setProperty( '--opacity', opacity );
	} else {
		tools.removeClass( el.ppl_inline_focused, 'b-ppl-inline--with-spotlight' );
		el.ppl_inline_focused.style.setProperty( '--opacity', '0' );
	}
};

/**
 * @function handleResize
 * @description Handle Browser Resize Events
 */
const handleResize = () => {
	const { viewport } = component;

	// Return if we haven't changed device types
	if ( getViewPort() === viewport ) {
		return;
	}

	// Update current viewport
	component.viewport = getViewPort();

	if ( component.viewport === 'mobile' ) {
		window.removeEventListener( 'scroll', handleSpotlightScroll );
		tools.removeClass( el.ppl_inline_focused, 'b-ppl-inline--visible' );
		el.ppl_inline_focused.style.setProperty( '--opacity', '0' );
	} else {
		window.addEventListener( 'scroll', handleSpotlightScroll );
	}
};

/**
 * @function bindEvents
 * @description Bind the events for this module here.
 */
const bindEvents = () => {
	on( document, 'modern_tribe/resize_executed', handleResize );
	window.addEventListener( 'scroll', handleSpotlightScroll );
};

/**
 * @function init
 * @description Initializes the class if the element(s) to work on are found.
 */

const init = () => {
	el.ppl_inline_focused = tools.getNodes( '.item-single__content .b-ppl-inline__ppl-focused', true, document, true )[ 0 ];

	if ( ! el.ppl_inline_focused || component.viewport === 'mobile' ) {
		return;
	}

	el.ppl_inline_height_half = el.ppl_inline_focused ? el.ppl_inline_focused.offsetHeight * .5 : 0;

	bindEvents();

	console.info( 'SquareOne Theme: Initialized PPL Inline block scripts.' );
};

export default init;
