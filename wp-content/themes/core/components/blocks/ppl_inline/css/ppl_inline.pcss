/* -----------------------------------------------------------------------------
 *
 * PPL Inline
 *
 * ----------------------------------------------------------------------------- */

.b-ppl-inline {
	&__ppl-focused {
		--opacity: 0;
		--container-height: 350px;

		margin-top: var(--spacer-70);

		&:not(:last-child) {
			margin-bottom: var(--spacer-70);
		}
	}

	margin-bottom: var(--spacer-40);

	&__title {
		margin-bottom: var(--spacer-20);
	}

	&__description {
		margin-bottom: var(--spacer-40);
	}

	&__iframe {
		border: solid 1px #eee;
		width: 100%;
		height: 540px;
		box-sizing: content-box;
	}

	&--with-spotlight {
		.b-ppl-inline {
			&__spotlight {
				z-index: 100000;
				background: #fff;
				position: absolute;
				max-width: var(--grid-width-staggered-double);
				border-radius: var(--border-radius-base);
				width: 100%;
			}
		}

		&:before {
			z-index: 10000;
			content: '';
			display: block;
			position: absolute;
			background: rgba(26, 26, 26, var(--opacity));
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			pointer-events: none;
		}

		&:after {
			content: '';
			display: block;
			height: var(--container-height);
		}
	}
}
