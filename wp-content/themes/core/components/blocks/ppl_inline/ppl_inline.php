<?php
declare( strict_types=1 );

use Tribe\Project\Post_Types\Page\Page;
use Tribe\Project\Templates\Components\blocks\ppl_inline\PPL_Inline_Block_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = PPL_Inline_Block_Controller::factory( $args );

if ( ! $c->ppl_is_active() && ! is_admin() && ! is_singular( Page::NAME ) ) {
	return null;
}
?>

<section <?php echo $c->get_classes(); ?> <?php echo $c->get_attrs(); ?>>
	<div <?php echo $c->get_container_classes(); ?>>

		<?php if ( ! $c->hide_title() && ! $c->is_ppl_focused() ) : ?>
			<?php echo $c->get_title(); ?>
		<?php endif; ?>

		<?php if ( ! $c->hide_description() && ! $c->is_ppl_focused() ) : ?>
			<?php echo $c->get_description(); ?>
		<?php endif; ?>

		<?php if ( $c->get_category_id() ) : ?>
			<iframe
				src="<?php echo $c->get_ppl_iframe_url( 'inline_iframe' ); ?>"
				title="Expert Advice Service"
				class="<?php echo $c->append_class_name( 'iframe' ); ?>"
			></iframe>
		<?php endif; ?>
	</div>
</section>
