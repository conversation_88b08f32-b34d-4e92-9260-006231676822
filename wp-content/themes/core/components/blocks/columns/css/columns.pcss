/* -----------------------------------------------------------------------------
 *
 * Columns
 *
 * ----------------------------------------------------------------------------- */

.b-columns {
	&__grid {
		@media (--viewport-medium) {
			display: grid;
			grid-column-gap: var(--spacer-40);
			grid-row-gap: var(--spacer-50);
		}

		.b-column {
			&:not(:last-child) {
				@media (--viewport-small-max) {
					margin-bottom: var(--spacer-50);
				}
			}
		}

		&--3-cols {
			grid-template-columns: 1fr 1fr 1fr;

			@media (--viewport-medium-max) {
				grid-template-columns: 1fr 1fr;
			}

			@media (--viewport-small-max) {
				grid-template-columns: 1fr;
			}
		}

		&--2-cols {
			grid-template-columns: 1fr 1fr;

			@media (--viewport-small-max) {
				grid-template-columns: 1fr;
			}
		}

		&--1-cols {
			grid-template-columns: 1fr;
		}

		&--33-66-2-cols {
			grid-template-columns: 1fr 2fr;

			@media (--viewport-small-max) {
				grid-template-columns: 1fr;
			}
		}

		&--66-33-2-cols {
			grid-template-columns: 2fr 1fr;

			@media (--viewport-small-max) {
				grid-template-columns: 1fr;
			}
		}
	}

	&__header-wrapper {
		margin-bottom: var(--spacer-50);
	}

	&--background-light {
		background-color: var(--color-background-light);
	}

	&--background-dark {
		background-color: var(--color-background-dark);

		color: var(--color-white);

		* {
			color: var(--color-white);
		}
	}

	&--has-background {
		padding-top: var(--spacer-50);
		padding-bottom: var(--spacer-50);

		.item-single--perfect-post-layout & {
			padding-left: var(--spacer-50);
			padding-right: var(--spacer-50);
			border-radius: var(--border-radius-media);
		}
	}

	&--mobile_carousel {
		@media (--viewport-small-max) {
			.c-slider__main {
				overflow-x: hidden;
				width: calc(100vw - var(--grid-gutter) * 2);
			}
		}
	}

	&--background-dark {
		.c-slider__pagination {
			.swiper-pagination-bullet {
				&:not(.swiper-pagination-bullet-active) {
					background: #fff;
				}
			}
		}
	}

	&--has-background {
		@media (--viewport-small-max) {
			.c-slider__main {
				width: calc(100vw - var(--grid-gutter) * 2 - var(--spacer-50) * 2);
			}
		}
	}

	&--vertically-aligned {
		.b-column {
			@media (--viewport-medium) {
				display: flex;
				align-items: center;
			}
		}
	}

	ol,
	ul {
		list-style-position: inside;
	}
}
