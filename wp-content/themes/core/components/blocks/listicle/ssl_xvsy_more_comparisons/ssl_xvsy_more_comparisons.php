<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_more_comparisons\ssl_XvsY_More_Comparisons_Block_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = ssl_XvsY_More_Comparisons_Block_Controller::factory( $args );

if ( ! $c->get_provider_a() || ! $c->get_provider_b() ) {
	if ( $error = $c->get_no_providers_error() ) {
		echo $error;
	}

	return;
}

$content = $c->get_comparison_template_content();

if ( ! $content ) {
	if ( $error = $c->get_no_content_error() ) {
		echo $error;
	}

	return;
}

?>
<section <?php echo $c->get_classes(); ?> <?php echo $c->get_attrs(); ?>>
	<div <?php echo $c->get_container_classes(); ?>>
		<?php

		get_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h3',
			Text_Controller::CONTENT => $c->get_section_title(),
		] );

		if ( $intro_text = $c->get_introduction() ) {
			echo $c->get_introduction();
		}

		echo $content;

		?>
	</div>
</section>
