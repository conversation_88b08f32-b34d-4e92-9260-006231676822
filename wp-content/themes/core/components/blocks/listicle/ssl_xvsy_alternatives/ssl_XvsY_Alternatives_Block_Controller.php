<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_alternatives;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Admin\Editor\Blocks_Utilities;
use Tribe\Project\Templates\Components\rating\Rating_Controller;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Models\ssl_Provider_Alternative;

class ssl_XvsY_Alternatives_Block_Controller extends ssl_XvsY_Controller {
	public const CONTAINER_CLASSES    = 'container_classes';
	public const CLASSES              = 'classes';
	public const ATTRS                = 'attrs';
	public const ROOT_CLASS           = 'b-ssl-xvsy-alternatives';
	public const INTRODUCTION_TEXT    = 'intro_text';
	public const ALTERNATIVES_QTY     = 2;
	public const DATALAYER_BLOCK_NAME = 'xvsy-alternatives';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $introduction_text;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->introduction_text = (string) $args[ self::INTRODUCTION_TEXT ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [ self::ROOT_CLASS . '__container' ],
			self::CLASSES           => [ 'c-block', self::ROOT_CLASS ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Alternatives To', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return string
	 */
	public function get_introduction(): string {
		return $this->introduction_text;
	}

	/**
	 * @param ssl_Provider_Alternative $alternative
	 *
	 * @return Deferred_Component
	 */
	private function get_provider_template( ssl_Provider_Alternative $alternative ): Deferred_Component {
		$content     = '';
		$info_header = '';

		if ( $logo = $alternative->get_logo_url() ) {
			$content = defer_template_part( 'components/image/image', null, [
				Image_Controller::IMG_URL      => esc_url( $logo ),
				Image_Controller::AS_BG        => false,
				Image_Controller::USE_LAZYLOAD => true,
				Image_Controller::CLASSES      => [ self::ROOT_CLASS . '__provider-logo' ],
				Image_Controller::IMG_ALT_TEXT => '',
			] );
		}

		if ( $score = $alternative->get_score() ) {
			$info_header = defer_template_part( 'components/rating/rating', null, [
				Rating_Controller::SCORE => ceil( ( (float) $score * 100 ) / 5 ),
				Rating_Controller::SHOW_STAR => false,
				Rating_Controller::MAX_SCORE => 100,
			] );
		}

		$info_header .= defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h5',
			Text_Controller::CONTENT => $alternative->get_name(),
		] );

		$info = defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__provider-info__header' ],
			Container_Controller::CONTENT => $info_header,
		] );

		if ( $description = $alternative->get_description() ) {
			$info .= defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'p',
				Text_Controller::CONTENT => esc_html( $description ),
			] );
		}

		if ( $redirect_link = $alternative->get_redirect_link() ) {
			$info .= defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => esc_url( $redirect_link ),
				Link_Controller::CONTENT => __( 'Visit website', 'tribe' ) . '<i class="icon icon-arrow-right"></i>',
				Link_Controller::TARGET  => '_blank',
				Link_Controller::ATTRS   => $alternative->get_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' ),
			] );
		}

		$content .= defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__provider-info' ],
			Container_Controller::CONTENT => $info,
		] );

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__provider' ],
			Container_Controller::CONTENT => $content,
		] );
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_provider_alternatives_content(): ?Deferred_Component {
		$alternatives = [];

		// Removing the current providers from the list
		$providers_to_exclude = [ $this->get_provider_a()->get_id(), $this->get_provider_b()->get_id() ];

		foreach ( [ $this->get_provider_a(), $this->get_provider_b() ] as $provider ) {
			$alternatives_to_include = $provider->get_alternatives( self::ALTERNATIVES_QTY, $providers_to_exclude );

			if ( $alternatives_to_include ) {
				$alternatives = [ ...$alternatives, ...$alternatives_to_include ];

				// Saving the added providers ids to not impact the next loop query
				$new_providers = array_map( function ( $alternative ) {
					return $alternative->get_provider_id();
				}, $alternatives_to_include );

				if ( $new_providers ) {
					$providers_to_exclude = [ ...$providers_to_exclude, ...$new_providers ];
				}
			}
		}

		if ( ! $alternatives || count( $alternatives ) < 2 ) {
			return null;
		}

		$content = '';

		foreach ( $alternatives as $alternative_provider ) {
			$content .= $this->get_provider_template( $alternative_provider );
		}

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__provider-container' ],
			Container_Controller::CONTENT => $content,
		] );
	}

	/**
	 * @return string
	 */
	public function get_section_title(): string {
		$provider_a = $this->get_provider_a();
		$provider_b = $this->get_provider_b();

		if ( ! $provider_a || ! $provider_b ) {
			return 'Alternatives';
		}

		return 'Alternatives to ' . $provider_a->get_name() . ' & ' . $provider_b->get_name();
	}

	/**
	 * @return string|null
	 */
	public function get_no_content_error(): ?string {
		return Blocks_Utilities::format_admin_error_message( 'No related content found or there is less than 2 providers to show. The block will not be rendered.' );
	}
}
