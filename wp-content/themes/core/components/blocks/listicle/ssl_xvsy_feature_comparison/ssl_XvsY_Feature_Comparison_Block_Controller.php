<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_feature_comparison;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Object_Meta\PPL_Options_Meta;
use Tribe\Project\Object_Meta\PPL_Settings;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Feature_Comparison\ssl_XvsY_Feature_Comparison;

class ssl_XvsY_Feature_Comparison_Block_Controller extends ssl_XvsY_Controller {
	public const CONTAINER_CLASSES = 'container_classes';
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const ROOT_CLASS        = 'b-ssl-xvsy-feature-comparison';
	public const INTRODUCTION_TEXT = 'intro_text';
	public const PRIMARY_CTA       = 'primary_cta';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $introduction_text;
	private string $primary_cta;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->introduction_text = (string) $args[ self::INTRODUCTION_TEXT ];
		$this->primary_cta       = (string) $args[ self::PRIMARY_CTA ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::PRIMARY_CTA 		=> '',
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [ 'c-block', self::ROOT_CLASS ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Feature Comparison', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return string
	 */
	public function get_accordion_content(): string {
		$introduction_text = '';

		if ( $intro = $this->introduction_text ) {
			$introduction_text = $intro;
		}

		$thead_row     = '<tr><th></th>';
		$tbody_rows    = '';
		$features_list = [];

		foreach ( [ $this->get_provider_a(), $this->get_provider_b() ] as $index => $provider ) {
			$thead_row .= '<th>' . esc_html( $provider->get_name() ) . '</th>';

			if ( $provider->get_features() ) {
				foreach ( $provider->get_features() as $feature ) {
					if ( ! isset( $features_list[ $feature->get_name() ] ) ) {
						$features_list[ $feature->get_name() ] = [ false, false ];
					}

					$features_list[ $feature->get_name() ][ $index ] = $feature->get_has_feature();
				}
			}
		}

		ksort( $features_list );

		if ( $features_list ) {
			foreach ( $features_list as $key => $values ) {
				$icon_a = $values[0] ? 'check' : 'close';
				$icon_b = $values[1] ? 'check' : 'close';

				$tbody_rows .= "<tr><th>$key</th><td><i class=\"icon icon-$icon_a\"></i></td><td><i class=\"icon icon-$icon_b\"></i></td></tr>";
			}
		}

		$thead_row .= '</tr>';

		$table = defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__table' ],
			Container_Controller::TAG     => 'table',
			Container_Controller::CONTENT => "<thead>$thead_row</thead><tbody>$tbody_rows</tbody>",
		] );

		$table_container_classes = [ self::ROOT_CLASS . '__table-container' ];

		if ( count( $features_list ) > 8 ) {
			$table_container_classes[] = self::ROOT_CLASS . '__table-container--has-more';

			$table_container_show_more = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'button',
				Text_Controller::ATTRS   => [ 'data-toggle-text' => 'Show Less' ],
				Text_Controller::CONTENT => __( 'Show More', 'tribe' ),
			] );

			$table .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__table-container__show-more' ],
				Container_Controller::CONTENT => $table_container_show_more,
			] );
		}

		$table_container = defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => $table_container_classes,
			Container_Controller::CONTENT => $table,
		] );

		$content = $introduction_text . $table_container;

		$ppl_btn = $this->get_primary_cta();

		if ( $ppl_btn ) {
			$custom_quote_text = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'p',
				Text_Controller::CONTENT => 'Get free help from our project management software advisors to find your match.',
			] );

			$content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__quote' ],
				Container_Controller::CONTENT => defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [ self::ROOT_CLASS . '__quote__container' ],
					Container_Controller::CONTENT => $custom_quote_text . $ppl_btn,
				] ),
			] );
		}

		return $content;
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_primary_cta(): ?Deferred_Component {
		$primary_cta = $this->primary_cta;
		$taxonomy_id = null;
		$classes     = [];

		$tool_name_a = sanitize_title( $this->get_provider_a()->get_name() );
		$tool_name_b = sanitize_title( $this->get_provider_b()->get_name() );

		if ( ! empty( $ppl_option_category_id = (string) get_field( PPL_Options_Meta::CATEGORY_ID, get_the_ID() ) ) ) {
			$taxonomy_id = $ppl_option_category_id;
		} else {
			$taxonomy_id = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_CATEGORY_ID_DEFAULT, 'option' );
		}

		if ( empty( $taxonomy_id ) ) {
			return null;
		}

		$classes = [
			'item-single__cta-link',
			'a-btn',
		];

		$ppl_url = '#modal-id-ppl-form||category-id-' . $taxonomy_id . '||post-id-' . get_the_ID();

		if ( $primary_cta === ssl_XvsY_Feature_Comparison::CTA_OPTION_GET_CUSTOM_QUOTE_UTM ) {
			$ppl_url .= '||utm-provider-' . $tool_name_a . '--provider-' . $tool_name_b;

			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Get Custom Quote', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );
		} elseif ( $primary_cta === ssl_XvsY_Feature_Comparison::CTA_OPTION_GET_EXPERT_ADVICE_NO_UTM ) {
			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Get Expert Advice', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );

		} elseif ( $primary_cta === ssl_XvsY_Feature_Comparison::CTA_OPTION_BOOK_DEMO_UTM ) {
			$ppl_url .= '||utm-provider-' . $tool_name_a . '--provider-' . $tool_name_b;

			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Book Demo', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );

		} else {
			return null;
		}
	}

	/**
	 * @return string
	 */
	public function get_accordion_title(): string {
		return $this->get_provider_a()->get_name() . ' vs. ' . $this->get_provider_b()->get_name() . ' Feature Comparison';
	}
}
