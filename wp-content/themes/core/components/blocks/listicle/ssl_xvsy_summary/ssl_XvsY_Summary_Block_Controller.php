<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_summary;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\Traits\ssl_Programmatic_Tool_Links;

class ssl_XvsY_Summary_Block_Controller extends ssl_XvsY_Controller {
	use ssl_Programmatic_Tool_Links;

	public const CONTAINER_CLASSES    = 'container_classes';
	public const CLASSES              = 'classes';
	public const ATTRS                = 'attrs';
	public const ROOT_CLASS           = 'b-ssl-xvsy-summary';
	public const VERDICT_USER         = 'verdict_user';
	public const VERDICT_ROLE         = 'verdict_role';
	public const DATALAYER_BLOCK_NAME = 'xvsy-summary';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private int    $verdict_user;
	private string $verdict_role;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->verdict_role      = (string) $args[ self::VERDICT_ROLE ];
		$this->verdict_user      = (int) $args[ self::VERDICT_USER ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [ 'c-block', self::ROOT_CLASS ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Expert Summary', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_verdict_content(): ?Deferred_Component {
		if ( ! $this->get_verdict() ) {
			return null;
		}

		$verdict_content = defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'span',
			Text_Controller::CLASSES => [ 't-overline' ],
			Text_Controller::CONTENT => __( 'OUR VERDICT', 'tribe' ),
		] );

		$verdict_content .= $this->get_verdict();

		$verdict_buttons          = '';
		$verdict_button_container = '';
		$verdict_person_container = '';

		if ( $redirect_link = $this->get_provider_a()->get_redirect_link() ) {
			$verdict_buttons .= defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => esc_url( $redirect_link ),
				Link_Controller::CONTENT => 'Visit ' . esc_html( $this->get_provider_a()->get_name() ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => [ 'a-btn' ],
				Link_Controller::ATTRS   => $this->get_provider_a_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' ),
			] );
		}

		if ( $redirect_link = $this->get_provider_b()->get_redirect_link() ) {
			$verdict_buttons .= defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => esc_url( $redirect_link ),
				Link_Controller::CONTENT => 'Visit ' . esc_html( $this->get_provider_b()->get_name() ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => [ 'a-btn' ],
				Link_Controller::ATTRS   => $this->get_provider_b_visit_link_attrs( self::DATALAYER_BLOCK_NAME, 'cta' ),
			] );
		}

		if ( $verdict_buttons ) {
			$verdict_button_container = defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__verdict__buttons' ],
				Container_Controller::CONTENT => $verdict_buttons,
			] );
		}

		if ( $this->verdict_user ) {
			$verdict_person_image = defer_template_part( 'components/image/image', null, [
				Image_Controller::IMG_URL      => Author_Controller::get_author_avatar_from_id( (int) $this->verdict_user ),
				Image_Controller::AS_BG        => false,
				Image_Controller::USE_LAZYLOAD => true,
				Image_Controller::CLASSES      => [ self::ROOT_CLASS . '__author-image' ],
				Image_Controller::IMG_ALT_TEXT => Author_Controller::get_author_display_name( $this->verdict_user ),
			] );

			$verdict_person_info_content = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'span',
				Text_Controller::CONTENT => Author_Controller::get_author_display_name( $this->verdict_user ),
			] );

			if ( $this->verdict_role ) {
				$verdict_person_info_content .= defer_template_part( 'components/text/text', null, [
					Text_Controller::TAG     => 'span',
					Text_Controller::CONTENT => esc_html( $this->verdict_role ),
				] );
			}

			$verdict_person_info = defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__verdict__person__info' ],
				Container_Controller::CONTENT => $verdict_person_info_content,
			] );

			$verdict_person_container = defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__verdict__person' ],
				Container_Controller::CONTENT => $verdict_person_image . $verdict_person_info,
			] );
		}

		$verdict_bottom_container = defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__verdict__bottom' ],
			Container_Controller::CONTENT => $verdict_person_container . $verdict_button_container,
		] );

		$verdict_content .= $verdict_bottom_container;

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__verdict' ],
			Container_Controller::CONTENT => $verdict_content,
		] );
	}

	/**
	 * @return string
	 */
	public function get_accordion_content(): string {
		$content = defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'p',
			Text_Controller::CONTENT => esc_html( $this->get_summary() ),
		] );

		if ( $this->get_verdict_content() ) {
			$content .= $this->get_verdict_content();
		}

		$providers = array_filter( [ $this->get_provider_a(), $this->get_provider_b() ] );
		if ( $providers && ( $programmatic_links = $this->get_programmatic_tool_links_lists( $providers, 'span', 't-overline' ) ) ) {
			$content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__links' ],
				Container_Controller::CONTENT => $programmatic_links,
			] );
		}

		return (string) $content;
	}

	/**
	 * @return string
	 */
	public function get_accordion_title(): string {
		return $this->get_provider_a()->get_name() . ' vs. ' . $this->get_provider_b()->get_name() . ': Expert Summary';
	}
}
