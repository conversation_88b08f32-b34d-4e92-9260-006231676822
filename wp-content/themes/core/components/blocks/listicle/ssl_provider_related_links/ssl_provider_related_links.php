<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\blocks\listicle\ssl_provider_related_links\ssl_Provider_Related_Links_Block_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = ssl_Provider_Related_Links_Block_Controller::factory( $args );

if ( ! $c->get_provider() ) {
	if ( $message = $c->get_no_provider_error() ) {
		echo $message;
	}

	return;
}

$content = $c->get_template_content();

if ( ! $content ) {
	if ( $error = $c->get_no_content_error() ) {
		echo $error;
	}

	return;
}

?>
<section <?php echo $c->get_classes(); ?> <?php echo $c->get_attrs(); ?>>
	<div <?php echo $c->get_container_classes(); ?>>
		<?php

		if ( $title = $c->get_section_title() ) {
			get_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'h3',
				Text_Controller::CONTENT => $title,
			] );
		}

		if ( $introduction = $c->get_introduction() ) {
			echo $introduction;
		}

		echo $content;

		?>
	</div>
</section>
