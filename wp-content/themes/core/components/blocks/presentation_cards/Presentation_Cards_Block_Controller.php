<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\presentation_cards;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Integrations\GSAP\GSAP;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Blocks\Types\Presentation_Cards\Presentation_Cards;
use \Tribe\Project\Blocks\Types\Content_Columns\Content_Columns;
use Tribe\Project\Templates\Models\Content_Column;
use Tribe\Project\Templates\Components\card\Card_Controller;
use Tribe\Project\Templates\Components\text\Heading_Tag;

class Presentation_Cards_Block_Controller extends Abstract_Controller {
	use Heading_Tag;

	public const CONTAINER_CLASSES = 'container_classes';
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const TITLE             = 'title';
	public const LINK_TEXT         = 'link_text';
	public const CTA               = 'cta';
	public const HEADING_TAG       = 'heading_tag';
	public const CTA_STYLE         = 'cta_style';
	public const CARDS             = 'cards';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $title;
	private string $link_text;
	private array  $cta;
	private string $heading_tag;
	private string $cta_style;
	private array  $cards;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->title             = (string) $args[ self::TITLE ];
		$this->link_text         = (string) $args[ self::LINK_TEXT ];
		$this->cta               = (array) $args[ self::CTA ];
		$this->heading_tag       = (string) $args[ self::HEADING_TAG ];
		$this->cta_style         = (string) $args[ self::CTA_STYLE ];
		$this->cards             = (array) $args[ self::CARDS ];

		$this->get_microinteractions_scripts();
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::TITLE             => '',
			self::LINK_TEXT         => '',
			self::CTA               => [],
			self::HEADING_TAG       => '',
			self::CTA_STYLE         => '',
			self::CARDS             => [],
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [ 'l-container' ],
			self::CLASSES           => [ 'c-block', 'b-presentation-cards' ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return Deferred_Component
	 */
	public function get_title(): Deferred_Component {
		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => $this->heading_tag,
			Text_Controller::CLASSES => [
				'c-block__title',
				'b-presentation-cards__title',
				'h3',
			],
			Text_Controller::CONTENT => $this->title ?? '',
		] );
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_content(): ?Deferred_Component {
		if ( ! $this->link_text && ! $this->cta ) {
			return null;
		}

		$link_text = defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'p',
			Text_Controller::CONTENT => $this->link_text ?? '',
		] );

		$cta = '';

		if ( ! empty( $this->cta ) ) {
			$this->cta[ Link_Controller::CLASSES ] = $this->get_cta_style();

			$cta = defer_template_part( 'components/link/link', null, $this->cta );
		}

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				'c-block__description',
				'b-presentation-cards__description',
				't-sink',
			],
			Container_Controller::CONTENT => $link_text . $cta,
		] );
	}

	private function get_cta_style(): array {
		if ( $this->cta_style === Presentation_Cards::STYLE_SECONDARY ) {
			$classes[] = sprintf( 'a-btn-%s', $this->cta_style );
		} elseif ( $this->cta_style === Presentation_Cards::STYLE_CTA ) {
			$classes[] = 'a-cta--secondary';
		} else {
			$classes[] = 'a-btn';
		}

		return $classes;
	}

	public function get_cards_array() {
		return $this->cards;
	}

	public function get_content_args( $card = [] ) {
		$highlight = $card[ Presentation_Cards::CARDS_HIGHLIGHT ] ?? '';
		$title     = $card[ Presentation_Cards::CARDS_TITLE ] ?? '';
		$content   = $card[ Presentation_Cards::CARDS_CONTENT ] ?? '';

		$title_tag = empty( $title ) ? $this->heading_tag : $this->next_hierarchy_tag( $this->heading_tag );

		$product_tag = null;

		if ( ! empty( $highlight ) ) {
			$product_tag = defer_template_part(
				'components/container/container',
				null,
				[
					Container_Controller::TAG     => 'p',
					Container_Controller::CONTENT => esc_html( $highlight ),
				]
			);
		}

		return [
			Card_Controller::PRODUCT_TAG => $product_tag,
			Card_Controller::TITLE       => defer_template_part(
				'components/text/text',
				null,
				[
					Text_Controller::CONTENT => esc_html( $title ),
					Text_Controller::TAG     => $title_tag,
					Text_Controller::CLASSES => [ 'h4' ],
				]
			),
			Card_Controller::DESCRIPTION => defer_template_part(
				'components/container/container',
				null,
				[
					Container_Controller::CLASSES => [
						'c-block__description',
						't-sink',
						's-sink',
					],
					Container_Controller::CONTENT => wp_kses_post( $content ),
				]
			),
		];
	}

	public function get_microinteractions_scripts(): void {
		if ( ! empty( $this->cards ) ) {
			GSAP::add_script( 'Simple_Embed_cards_presentation', $this->get_microinteractions_cards_presentation_gsap_script() );
		}
	}

	private function get_microinteractions_cards_presentation_gsap_script(): ?string {
		if ( wp_is_mobile() ) {
			return null;
		}

		return <<<JAVASSCRIPT
			<script>
				document.addEventListener("bwz/gsap_loaded", () => {
					// Prevent running more than once
					if (window.__bwzPresentationCardsAnimated) return;
					window.__bwzPresentationCardsAnimated = true;

					if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') return;

					const section = document.querySelector('.b-presentation-cards__animation-wrapper');
					if (!section) return;

					const cards = section.querySelectorAll('.b-presentation-cards__card .c-card');
					if (!cards.length) return;

					const end = cards.length * 200; // Adjust based on the number of cards

					gsap.registerPlugin(ScrollTrigger);

					const tl = gsap.timeline({
						scrollTrigger: {
							trigger: section,
							start: 'top 25%',
							end: '+=' + end + '%',
							once: true,
							pin: true,
							anticipatePin: 1,
							onComplete: () => {
								// Let ScrollTrigger handle the unpinning naturally
								// and refresh to recalculate positions
								requestAnimationFrame(() => {
									ScrollTrigger.refresh();
								});
							},
							onLeave: () => {
								// Additional cleanup if needed
								requestAnimationFrame(() => {
									ScrollTrigger.refresh();
								});
							}
						}
					});

					cards.forEach((card) => {
						// Fade in current card
						tl.fromTo(card,
							{ opacity: 0, y: 100 },
							{
								opacity: 1,
								y: 0,
								duration: 1,
								onStart: () => {
									card.classList.add('c-card--active');
								},
							}
						);

						// Lighten background once the next card starts to appear
						tl.to(card, {
							duration: 0.5,
							onStart: () => {
								card.classList.remove('c-card--active');
							}
						}, '+=0.5');
					});
				});
			</script>
		JAVASSCRIPT;
	}

}
