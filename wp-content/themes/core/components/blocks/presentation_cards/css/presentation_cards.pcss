/* -----------------------------------------------------------------------------
 *
 * Presentation Cards
 *
 * ----------------------------------------------------------------------------- */

.b-presentation-cards {
	&__title {
		margin-bottom: var(--spacer-70);
		text-align: center;
	}

	&__description {
		text-align: center;

		p {
			@mixin t-body-small;
			display: inline-block;
			margin-right: var(--spacer-20);
		}
	}

	&__card {
		.c-card {
			background-color: var(--color-background-light);
			padding: var(--spacer-30) var(--spacer-40);
			border-radius: var(--border-radius-media);
			transition: background-color 0.3s;

			&--active {
				background-color: var(--color-background-dark);
				color: var(--color-white);

				.c-card__title {
					color: var(--color-secondary);
				}
			}

			&__title {
				@mixin t-display-xxx-small;
				color: var(--color-primary);

				@media (--viewport-medium) {
					@mixin t-display-small;
				}

				&:hover {
					text-decoration: none;
					cursor: default;
				}
			}

			&__product-tag {
				margin-bottom: var(--spacer-20);

				p {
					font-weight: var(--font-weight-semibold);
				}
			}

			&__description {
				margin-bottom: 0;
			}
		}
	}
}
