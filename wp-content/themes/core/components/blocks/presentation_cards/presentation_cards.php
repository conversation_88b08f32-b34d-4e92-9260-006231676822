<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\blocks\presentation_cards\Presentation_Cards_Block_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = Presentation_Cards_Block_Controller::factory( $args );

$cards_count = count( $c->get_cards_array() );
?>

<section <?php echo $c->get_classes(); ?> <?php echo $c->get_attrs(); ?>>
	<div class="b-presentation-cards__animation-wrapper">
		<div <?php echo $c->get_container_classes(); ?>>
			<?php if ( $c->get_title() ) : ?>
				<?php echo $c->get_title(); ?>
			<?php endif; ?>

			<div class="<?php echo sprintf( 'g-%d-up', $cards_count > 1 ? $cards_count : 2 ); ?>">

				<?php foreach ( $c->get_cards_array() as $card ) { ?>
					<div class="b-presentation-cards__card">
						<?php get_template_part(
							'components/card/card',
							null,
							$c->get_content_args( $card )
						);
						?>
					</div>
				<?php } ?>

			</div>

			<?php if ( $c->get_content() ) : ?>
				<?php echo $c->get_content(); ?>
			<?php endif; ?>

			<?php if ( ! is_admin() ) {
				wp_enqueue_script( 'scrollTriggerGSAP', 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js' );
			} ?>
		</div>
	</div>
</section>
