<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\sidebar_post;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Integrations\Adsanity\Tribe_Adsanity;
use Tribe\Project\Object_Meta\Download_Settings_Meta;
use Tribe\Project\Object_Meta\Media_Settings_Meta;
use Tribe\Project\Object_Meta\Member_Stream_Settings_Meta;
use Tribe\Project\Object_Meta\Post_Meta;
use Tribe\Project\Object_Meta\Tool_Post_Meta;
use Tribe\Project\Object_Meta\Post_Sidebar_Settings_Meta;
use Tribe\Project\Object_Meta\Post_Settings_Meta;
use Tribe\Project\Object_Meta\Tool_Post_Settings_Meta;
use Tribe\Project\Object_Meta\PPL_Settings;
use Tribe\Project\Object_Meta\PPL_Options_Meta;
use Tribe\Project\Post_Types\Download\Download;
use Tribe\Project\Post_Types\Media\Media;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Post_Types\Tool_Post\Tool_Post_Template_In_Depth_Reviews;
use Tribe\Project\Templates\Components\author\Author_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\related_posts\Related_Posts_Controller;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\button\Button_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\blocks\listicle\ssl_3_pack_ad\ssl_3_Pack_Ad_Block_Controller;
use Tribe\Project\Templates\Components\blocks\inline_newsletter\Inline_Newsletter_Block_Controller;
use Tribe\Project\Templates\Components\blocks\simple_embed\Simple_Embed_Block_Controller;
use Tribe\Project\Templates\Components\table_of_contents\Table_Of_Contents_Controller;
use Tribe\Project\Object_Meta\In_Depth_Reviews_Meta;
use Tribe\Project\Templates\Components\Traits\Tag_Loader;
use Tribe\Project\Theme\Config\Image_Sizes;
use Tribe\Project\Post_Types\Tool_Post\Tool_Post;
use Tribe\Project\Post_Types\Post\Post;

class Sidebar_Post_Controller extends Abstract_Controller {
	use Tag_Loader;

	public string $ppl_advisor_banner_utm_content = '';

	public const CLASSES       = 'classes';
	public const HIDE_TOC      = 'hide_toc';
	public const PRIMARY_CTA   = 'primary_cta';
	public const SECONDARY_CTA = 'secondary_cta';

	private array               $classes;
	private bool                $hide_toc;
	private ?Deferred_Component $primary_cta;
	private ?Deferred_Component $secondary_cta;
	private ?string             $content;

	public function __construct( array $args = [] ) {
		$args = $this->parse_args( $args );

		$this->classes       = (array) $args[ self::CLASSES ];
		$this->hide_toc      = $args[ self::HIDE_TOC ] ? (bool) $args[ self::HIDE_TOC ] : false;
		$this->primary_cta   = $args[ self::PRIMARY_CTA ] ?: null;
		$this->secondary_cta = $args[ self::SECONDARY_CTA ] ?: null;
		$this->content       = null;

		$this->generate_sidebar_content();
	}

	protected function defaults(): array {
		return [
			self::CLASSES       => [ 'c-sidebar-post' ],
			self::HIDE_TOC      => false,
			self::PRIMARY_CTA   => null,
			self::SECONDARY_CTA => null,
		];
	}

	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	private function is_article(): bool {
		return is_singular( Post::NAME );
	}

	private function is_in_depth_review(): bool {
		return is_page_template( Tool_Post_Template_In_Depth_Reviews::TEMPLATE_FILE );
	}

	public function generate_sidebar_content() {
		$sidebar_content = '';
		$tag             = false;

		if ( $this->is_article() ) {
			$blocks = get_field( Post_Sidebar_Settings_Meta::POST_SIDEBAR, 'option' );

			if ( ! $blocks ) {
				return null;
			}

			$maximum_sidebar_size = $this->get_sidebar_size();

			if ( count( $blocks ) > $maximum_sidebar_size ) {
				$blocks = array_slice( $blocks, 0, $maximum_sidebar_size );
			}

			foreach ( $blocks as $key => $block ) {
				if ( $block[ Post_Sidebar_Settings_Meta::POST_SIDEBAR_BLOCK ] === Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_AD ) {
					if ( $this->get_replace_main_ad_for_three_pack() && ( $key <= 1 ) ) {
						$sidebar_content .= $this->get_single_block(
							$this->render_sidebar_three_pack_ad(),
							[
								'c-sidebar-post__item-container',
								'c-sidebar-post__sidebar-item--3-pack-tool-highlight',
							]
						);
					} else {
						$sidebar_content .= $this->get_single_block(
							$this->render_sidebar_ad( $block ),
							[
								'c-sidebar-post__item-container',
								'c-sidebar-post__sidebar-item--ad',
							]
						);
					}
				} elseif ( $block[ Post_Sidebar_Settings_Meta::POST_SIDEBAR_BLOCK ] === Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_NEWSLETTER ) {
					if ( $this->is_ppl_focused_content() && $this->ppl_is_active() ) {
						$content         = $this->render_sidebar_ppl_banner_advisor( 'ppl-modal-sidebar' );
						$sidebar_content .= $this->get_single_block(
							$content,
							[
								'c-sidebar-post__item-container',
								'c-sidebar-post__sidebar-item--ppl-advisor',
							],
							true
						);
					} else {
						[ $content, $tag ] = $this->render_sidebar_newsletter( $block );
						$sidebar_content .= $this->get_single_block(
							$content,
							[
								'c-sidebar-post__item-container',
								'c-sidebar-post__sidebar-item--newsletter',
								$tag ? 'c-sidebar-post__sidebar-item--with-tag' : '',
							],
							true
						);
					}
				} elseif ( $block[ Post_Sidebar_Settings_Meta::POST_SIDEBAR_BLOCK ] === Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_RELATED_POSTS ) {
					if ( ! $this->hide_sidebar_recommendations() ) {
						[ $content, $tag ] = $this->render_sidebar_related_posts( $block );
						$sidebar_content .= $this->get_single_block(
							$content,
							[
								'c-sidebar-post__item-container',
								'c-sidebar-post__sidebar-item--links-menu',
								$tag ? 'c-sidebar-post__sidebar-item--with-tag' : '',
							]
						);
					}
				} elseif ( $block[ Post_Sidebar_Settings_Meta::POST_SIDEBAR_BLOCK ] === Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_TOC ) {
					$wrapped = true;

					if ( $this->is_article() && $key === 0 ) {
						$this->classes[] = 'c-sidebar-post--fixed';
						$wrapped         = false;
					}

					$sidebar_content .= $this->get_single_block(
						defer_template_part( 'components/table_of_contents/table_of_contents', null, [] ),
						[
							'c-sidebar-post__sidebar-item--toc-menu',
							$tag ? 'c-sidebar-post__sidebar-item--with-tag' : '',
						],
						$wrapped
					);
				}
			}
		} else {
			if ( ! $this->is_in_depth_review() ) {
				if ( ! $this->get_replace_main_ad_for_three_pack() &&
					! empty( $ad = $this->render_sidebar_tool_ad() )
				) {
					$sidebar_content .= $this->get_single_block(
						defer_template_part( 'components/table_of_contents/table_of_contents', null, [] ) . $ad,
						[ 'c-sidebar-post__sidebar-item--toc-menu' ],
						true
					);
				} else {
					$sidebar_content .= $this->get_single_block(
						defer_template_part( 'components/table_of_contents/table_of_contents', null, [] ),
						[ 'c-sidebar-post__sidebar-item--toc-menu' ],
						true
					);
				}
			} else {
				$indepth_sidebar_content = defer_template_part( 'components/table_of_contents/table_of_contents', null, [
					Table_Of_Contents_Controller::HIDE_LINKS    => $this->hide_toc,
					Table_Of_Contents_Controller::PRIMARY_CTA   => $this->primary_cta,
					Table_Of_Contents_Controller::SECONDARY_CTA => $this->secondary_cta,
				] );

				$video = $this->render_sidebar_in_depth_review_main_video();

				if ( $video ) {
					$indepth_sidebar_content .= $video;
				}

				$sidebar_content .= defer_template_part( 'components/container/container', null, [
					Container_Controller::TAG     => 'div',
					Container_Controller::CLASSES => [
						'c-sidebar-post__item-container',
					],
					Container_Controller::CONTENT => defer_template_part( 'components/container/container', null, [
						Container_Controller::TAG     => 'div',
						Container_Controller::CLASSES => [
							'c-sidebar-post__sidebar-item',
							'c-sidebar-post__sidebar-item--toc-menu',
						],
						Container_Controller::CONTENT => $indepth_sidebar_content,
					] ),
				] );
			}
		}

		if ( $sidebar_content ) {
			$this->content = $sidebar_content;

			$has_table_of_contents = str_contains( $sidebar_content, 'c-table-of-contents' );

			if ( $this->is_in_depth_review() && $has_table_of_contents ) {
				$this->classes[] = 'c-sidebar-post--fixed';
			}
		}
	}

	public function get_content(): ?string {
		return $this->content;
	}

	public function render_sidebar_ppl_banner_advisor( $utm_content, $return = true ): bool|Deferred_Component|null {
		$this->ppl_advisor_banner_utm_content = $utm_content;

		$content = $this->get_sidebar_ppl_banner_content();
		$classes = [];

		if ( ! empty( $content ) ) {
			if ( $return ) {
				return $this->get_single_block( $content, $classes, false );
			}

			echo $this->get_single_block( $content, $classes, false );

			return true;
		}

		return false;
	}

	public function get_sidebar_ppl_banner_content(): ?Deferred_Component {
		$ppl_banner_title = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_SIDEBAR_BANNER_TITLE, 'option' ) ?? null;
		$ppl_banner_copy  = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_SIDEBAR_BANNER_CONTENT, 'option' ) ?? null;

		$ppl_banner_advisor         = (int) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_SIDEBAR_BANNER_ADVISOR, 'option' ) ?? 0;
		$ppl_banner_advisor_picture = (int) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_SIDEBAR_BANNER_ADVISOR_PICTURE, 'option' ) ?? 0;
		$ppl_banner_advisor_name    = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_SIDEBAR_BANNER_ADVISOR_NAME, 'option' ) ?? null;

		$ppl_banner_badge         = (int) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_SIDEBAR_BANNER_ADVISOR_BADGE, 'option' ) ?? 0;
		$ppl_banner_advisor_title = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_SIDEBAR_BANNER_ADVISOR_TITLE, 'option' ) ?? null;

		$content = '';

		$content .= defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'p',
			Text_Controller::CLASSES => [
				'c-sidebar-post__sidebar-item--ppl-advisor__title',
			],
			Text_Controller::CONTENT => $ppl_banner_title ?? '',
		] );

		if ( $ppl_banner_advisor || ( $ppl_banner_advisor_picture && $ppl_banner_advisor_name ) ) {
			$img_url = wp_get_attachment_image_url( $ppl_banner_badge, Image_Sizes::SIDEBAR_PPL_BANNER_BADGE );

			if ( $img_url ) {
				$img = defer_template_part( 'components/image/image', null, [
					Image_Controller::IMG_URL      => esc_url( $img_url ),
					Image_Controller::AS_BG        => true,
					Image_Controller::USE_LAZYLOAD => true,
					Image_Controller::CLASSES      => [ 'c-sidebar-post__sidebar-item--ppl-advisor__avatar' ],
					Image_Controller::IMG_ALT_TEXT => esc_attr( $ppl_banner_title ),
					Image_Controller::IMG_CLASSES  => [ 'c-sidebar-post__sidebar-item--ppl-advisor__avatar__img' ],
				] );
			} else {
				if ( $ppl_banner_advisor_title ) {
					$img = '<p class="c-sidebar-post__sidebar-item--ppl-advisor__info__advisor-title">' . $ppl_banner_advisor_title . '</p>';
				} else {
					$img = '<p class="c-sidebar-post__sidebar-item--ppl-advisor__info__advisor-title">Advisor</p>';
				}
			}

			if ( $ppl_banner_advisor ) {
				$advisor              = Author_Controller::factory( [
					Author_Controller::AUTHOR_ID   => $ppl_banner_advisor,
					Author_Controller::AVATAR_SIZE => 64,
				] );
				$advisor_display_name = $advisor->get_author_display_name( $ppl_banner_advisor );

				$right_side_advisor_info = defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [
						'c-sidebar-post__sidebar-item--ppl-advisor__info',
					],
					Container_Controller::CONTENT => $advisor_display_name . $img ?? '',
				] );

				$content .= defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [
						'c-sidebar-post__sidebar-item--ppl-advisor__info-wrapper',
					],
					Container_Controller::CONTENT => '<img src=' . $advisor->get_author_avatar() . ' alt="Tool Advisor - ' . $advisor_display_name . '"/>' . $right_side_advisor_info ?? '',
				] );
			} else {
				$right_side_advisor_info = '<div class="c-sidebar-post__sidebar-item--ppl-advisor__info">' . $ppl_banner_advisor_name . $img . '</div>';

				$content .= defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [
						'c-sidebar-post__sidebar-item--ppl-advisor__info-wrapper',
					],
					Container_Controller::CONTENT => '<img src=' . wp_get_attachment_image_url( $ppl_banner_advisor_picture, Image_Sizes::SQUARE_XSMALL ) . ' alt="Tool Advisor - ' . $ppl_banner_advisor_name . '"/>' . $right_side_advisor_info ?? '',
				] );
			}
		}

		$content .= defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'p',
			Text_Controller::CLASSES => [
				'c-sidebar-post__sidebar-item--ppl-advisor__content__description',
			],
			Text_Controller::CONTENT => $ppl_banner_copy ?? '',
		] );

		$content .= $this->get_sidebar_ppl_banner_cta();

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				'c-sidebar-post__sidebar-item--ppl-advisor__wrapper',
			],
			Container_Controller::CONTENT => $content,
		] );
	}

	private function get_ppl_banner_url(): ?string {
		$ppl_banner_cta_url = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_SHORTLIST_BANNER_CTA_URL_DEFAULT, 'option' ) ?? null;

		if ( ! $ppl_banner_cta_url ) {
			if ( ! empty( $ppl_option_category_id = (string) get_field( PPL_Options_Meta::CATEGORY_ID, get_the_ID() ) ) ) {
				$taxonomy_id = $ppl_option_category_id;
			} else {
				$taxonomy_id = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_CATEGORY_ID_DEFAULT, 'option' );
			}

			$ppl_banner_cta_url = '#modal-id-' . $this->ppl_advisor_banner_utm_content . '||category-id-' . $taxonomy_id . '||post-id-' . get_the_ID();
		}

		return $ppl_banner_cta_url;
	}

	public function get_sidebar_ppl_banner_cta(): Deferred_Component {
		$ppl_banner_cta_label = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_SHORTLIST_BANNER_CTA_CONTENT_DEFAULT, 'option' ) ?? null;
		$ppl_banner_cta_url   = $this->get_ppl_banner_url();

		$button = defer_template_part( 'components/link/link', null, [
			Link_Controller::ID      => 'c-sidebar-post__sidebar-item--ppl-advisor__cta__link',
			Link_Controller::URL     => $ppl_banner_cta_url,
			Link_Controller::TARGET  => '_self',
			Link_Controller::CLASSES => [ 'a-btn-secondary', 'c-sidebar-post__sidebar-item--ppl-advisor__cta__link' ],
			Link_Controller::CONTENT => $ppl_banner_cta_label,
		] );

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				'c-sidebar-post__sidebar-item--ppl-advisor__cta',
			],
			Container_Controller::CONTENT => $button ?? '',
		] );
	}

	/**
	 * @return Deferred_Component
	 */
	public function render_sidebar_three_pack_ad(): Deferred_Component {
		return defer_template_part( 'components/blocks/listicle/ssl_3_pack_ad/ssl_3_pack_ad', null, [
			Ssl_3_Pack_Ad_Block_Controller::CLASSES => [ 'b-3-pack-tool-highlight--sidebar' ],
			Ssl_3_Pack_Ad_Block_Controller::TITLE   => __( 'Featured Tools', 'tribe' ),
		] );
	}

	/**
	 * @param array $block
	 *
	 * @return bool|string|null
	 */
	public function render_sidebar_ad( array $block ): string|bool|null {
		if ( ! empty( $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_AD_AD_GROUP ] ) ) {
			$slot_id = $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_AD_AD_GROUP ];
		} else {
			$slot_id = get_field( Post_Settings_Meta::NAME . '_' . Post_Settings_Meta::AD_GROUP_CONTENT_SIDEBAR, 'option' );
		}

		if ( ! $slot_id ) {
			return null;
		}

		if ( ! $this->has_adsanity_ad_running( $slot_id ) ) {
			return null;
		}

		return $this->render_ad( $slot_id, 'content-sidebar', true );
	}

	public function has_adsanity_ad_running( $ad_group ): bool {
		if ( $this->sponsored_content() ) {
			return false;
		}

		if ( $ad_group ) {
			$args = [
				Tribe_Adsanity::GROUP_IDS   => [ $ad_group ],
				Tribe_Adsanity::NUM_COLUMNS => 1,
				Tribe_Adsanity::NUM_ADS     => 1,
			];

			return Tribe_Adsanity::adsanity_has_active_ad_in_ad_group( $args );
		} else {
			return false;
		}
	}

	public function render_ad( $post_ad_group, $slot_id = '', bool $return = false ) {
		if ( ! $this->sponsored_content() ) {
			$id        = get_the_ID();
			$post_type = get_post_type( $id );
			$layout    = 'inline';

			if ( $post_ad_group === Post_Settings_Meta::AD_GROUP_FOOTER || $post_ad_group === Media_Settings_Meta::AD_GROUP_FOOTER || $post_ad_group === Tool_Post_Settings_Meta::AD_GROUP_FOOTER || $post_ad_group === Member_Stream_Settings_Meta::AD_GROUP_FOOTER ) {
				$layout = 'footer';
			}

			if ( (int) $post_ad_group > 0 ) {
				$ad_group = $post_ad_group;
			} else {
				switch ( $post_type ) {
					case Post::NAME:
						$ad_group = get_field( Post_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					case Media::NAME:
						$ad_group = get_field( Media_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					case Tool_Post::NAME:
						$ad_group = get_field( Tool_Post_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					case Member_Stream::NAME:
						$ad_group = get_field( Member_Stream_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					case Download::NAME:
						$ad_group = get_field( Download_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
						break;
					default:
						$ad_group = get_field( Post_Settings_Meta::NAME . '_' . $post_ad_group, 'option' );
				}
			}

			if ( $ad_group ) {
				$args = [
					Tribe_Adsanity::GROUP_IDS   => [ $ad_group ],
					Tribe_Adsanity::NUM_COLUMNS => 1,
					Tribe_Adsanity::SLOT_ID     => $slot_id ?? null,
					Tribe_Adsanity::RETURN      => $return,
					Tribe_Adsanity::NUM_ADS     => 1,
				];

				return Tribe_Adsanity::adsanity_show_ad_group( $args, $layout );
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	public function sponsored_content(): bool {
		return (bool) $this->get_field( Post_Meta::HIDE_ADS ) ?? false;
	}

	/**
	 * @param array $block
	 *
	 * @return array
	 */
	public function render_sidebar_newsletter( array $block ): array {
		$content = defer_template_part( 'components/blocks/inline_newsletter/inline_newsletter', null, [
			Inline_Newsletter_Block_Controller::NEWSLETTER_TAG         => $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_NEWSLETTER_TAG ],
			Inline_Newsletter_Block_Controller::NEWSLETTER_TAG_TITLE   => $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_NEWSLETTER_TAG_TITLE ],
			Inline_Newsletter_Block_Controller::NEWSLETTER_TAG_THEME   => $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_NEWSLETTER_TAG_THEME ],
			Inline_Newsletter_Block_Controller::NEWSLETTER_CONTENT     => $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_NEWSLETTER_CONTENT ],
			Inline_Newsletter_Block_Controller::NEWSLETTER_CUSTOM_FORM => $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_NEWSLETTER_CUSTOM_FORM ],
			Inline_Newsletter_Block_Controller::NEWSLETTER_FORM        => $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_NEWSLETTER_FORM ],
			Inline_Newsletter_Block_Controller::NEWSLETTER_HIDE_IMAGE  => $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_NEWSLETTER_HIDE_IMAGE ],
		] );

		return [
			$content,
			$block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_NEWSLETTER_TAG ],
		];
	}

	/**
	 * @param array $block
	 *
	 * @return bool|array
	 */
	public function render_sidebar_related_posts( array $block ): bool|array {
		$content = '';

		if ( $block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_RELATED_POSTS_TAG ] ) {
			$content .= $this->get_tag(
				$block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_RELATED_POSTS_TAG_TITLE ],
				[
					'c-sidebar-post__sidebar-item__tag-container',
				],
				$block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_RELATED_POSTS_TAG_THEME ],
			);
		}

		if ( $related_posts = Related_Posts_Controller::get_primary_category_posts() ) {
			$related_posts = $related_posts[ Related_Posts_Controller::POSTS ];

			if ( ! $related_posts ) {
				return false;
			}

			if ( count( $related_posts ) > 3 ) {
				$related_posts = array_slice( $related_posts, 0, 3 );
			}

			foreach ( $related_posts as $post ) {
				$image          = '';
				$post_classes   = [];
				$post_classes[] = 'c-sidebar-post__sidebar-item__related-post';
				$post_title     = $post[ Related_Posts_Controller::POST_TITLE ] ?? '';
				$post_url       = $post[ Related_Posts_Controller::POST_LINK ][ Link_Controller::URL ] ?? '#';
				$post_image_id  = $post[ Related_Posts_Controller::POST_IMAGE_ID ] ?? '';

				$title = defer_template_part( 'components/text/text', null, [
					Text_Controller::CONTENT => $post_title,
					Text_Controller::TAG     => 'p',
					Text_Controller::CLASSES => [ 'c-sidebar-post__sidebar-item__related-post-title' ],
				] );

				if ( ! empty( $post_image_id ) ) {
					$image = defer_template_part( 'components/image/image', null, [
						Image_Controller::IMG_ID       => $post_image_id,
						Image_Controller::SRC_SIZE     => Image_Sizes::SQUARE_XSMALL,
						Image_Controller::AS_BG        => true,
						Image_Controller::USE_LAZYLOAD => true,
						Image_Controller::CLASSES      => [ 'c-sidebar-post__sidebar-item__related-post-thumbnail' ],
						Image_Controller::IMG_ALT_TEXT => $post_title ? esc_attr( $post_title ) : 'Related Post Thumbnail',
						Image_Controller::IMG_CLASSES  => [ 'c-sidebar-post__sidebar-item__related-post-thumbnail-img' ],
					] );
				} else {
					$post_classes[] = 'c-sidebar-post__sidebar-item__related-post--no-image';
				}

				$content .= defer_template_part( 'components/link/link', null, [
					Link_Controller::CONTENT => $image . $title,
					Link_Controller::URL     => $post_url,
					Link_Controller::ID      => 'related-post-' . sanitize_title( $post_title ),
					Link_Controller::CLASSES => $post_classes,
				] );
			}
		}

		if ( ! empty( $content ) ) {
			$content = defer_template_part( 'components/container/container', null, [
				Container_Controller::CONTENT => $content,
				Container_Controller::TAG     => 'div',
				Container_Controller::CLASSES => [
					'c-sidebar-post__sidebar-item--related-posts',
				],
			] );

			return [
				$content,
				$block[ Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_RELATED_POSTS_TAG ],
			];
		}

		return false;
	}

	public function render_sidebar_in_depth_review_main_video(): ?Deferred_Component {
		// FIXME: Remove to have video in sidebar
		return null;

		$video_url       = get_field( In_Depth_Reviews_Meta::VIDEO_URL, get_the_ID() );
		$video_thumbnail = get_field( In_Depth_Reviews_Meta::VIDEO_THUMBNAIL, get_the_ID() );

		if ( ! $video_url ) {
			return null;
		}

		$video = defer_template_part( 'components/button/button', null, [
			Button_Controller::CLASSES => [ 'c-sidebar-post__sidebar-video__close-button' ],
			Button_Controller::TYPE    => 'button',
			Button_Controller::CONTENT => '<span class="icon icon-close"></span>',
		] );

		$video .= defer_template_part( 'components/blocks/simple_embed/simple_embed', null, [
			Simple_Embed_Block_Controller::THUMBNAIL => isset( $video_thumbnail ) ? (int) $video_thumbnail : 0,
			Simple_Embed_Block_Controller::LINK      => $video_url,
		] );

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'div',
			Container_Controller::CLASSES => [
				'c-sidebar-post__sidebar-video',
			],
			Container_Controller::CONTENT => $video,
		] );
	}

	public function get_video_layout_container_attrs( $provider_name, $embed_id, $title ): array {
		return [
			'data-js'             => 'c-video',
			'data-embed-id'       => $embed_id,
			'data-embed-provider' => $provider_name,
			'data-embed-title'    => $title,
		];
	}

	private function get_video_id( $video_link ): string {
		$url = parse_url( $video_link );

		if ( ! $url ) {
			return '';
		}

		if ( $url['host'] === 'youtu.be' ) {
			// get the path as video_id
			$video_id = ltrim( $url['path'], '/' );
		} else {
			$query = [];
			parse_str( $url['query'], $query );
			$video_id = $query['v'];
		}

		if ( ! $video_id ) {
			return '';
		}

		return $video_id;
	}

	public function get_single_block( $content, $classes = [], $wrapped = true ): ?Deferred_Component {
		$block_container = defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'div',
			Container_Controller::CLASSES => [
				'c-sidebar-post__sidebar-item',
				...$classes,
			],
			Container_Controller::CONTENT => $content,
		] );

		if ( $wrapped ) {
			return defer_template_part( 'components/container/container', null, [
				Container_Controller::TAG     => 'div',
				Container_Controller::CLASSES => [
					'c-sidebar-post__item-container',
				],
				Container_Controller::CONTENT => $block_container,
			] );
		}

		return $block_container;
	}

	public function get_sidebar_size(): int {
		return $this->get_field( Post_Meta::SIDEBAR_SIZE ) ? (int) $this->get_field( Post_Meta::SIDEBAR_SIZE ) : Post_Meta::SIDEBAR_SIZE_DEFAULT;
	}

	public function get_sidebar_blocks(): array {
		$blocks = get_field( Post_Sidebar_Settings_Meta::POST_SIDEBAR, 'option' );

		if ( ! $blocks ) {
			return [];
		}

		return $blocks;
	}

	public function get_replace_main_ad_for_three_pack(): bool {
		if ( $this->is_article() ) {
			return $this->get_field( Post_Meta::REPLACE_MAIN_AD_FOR_THREE_PACK ) === null ? false : $this->get_field( Post_Meta::REPLACE_MAIN_AD_FOR_THREE_PACK );
		} else {
			return $this->get_field( Tool_Post_Meta::REPLACE_MAIN_AD_FOR_THREE_PACK ) === null ? false : $this->get_field( Tool_Post_Meta::REPLACE_MAIN_AD_FOR_THREE_PACK );
		}
	}

	public function first_block_is_ad_or_toc(): bool {
		$blocks = $this->get_sidebar_blocks();

		if ( ! $blocks ) {
			return false;
		}

		if ( $this->get_replace_main_ad_for_three_pack() ) {
			return false;
		}

		if ( $blocks[0][ Post_Sidebar_Settings_Meta::POST_SIDEBAR_BLOCK ] === Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_AD && ! $this->render_sidebar_ad( $blocks[0] ) ) {
			return false;
		}

		if ( $blocks[0][ Post_Sidebar_Settings_Meta::POST_SIDEBAR_BLOCK ] === Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_TOC && ! $this->has_TOC() ) {
			return false;
		}

		return $blocks[0][ Post_Sidebar_Settings_Meta::POST_SIDEBAR_BLOCK ] === Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_TOC
			|| $blocks[0][ Post_Sidebar_Settings_Meta::POST_SIDEBAR_BLOCK ] === Post_Sidebar_Settings_Meta::SIDEBAR_BLOCK_AD;
	}

	public function has_TOC(): bool {
		$TOC = defer_template_part( 'components/table_of_contents/table_of_contents', null, [] );

		if ( ! $TOC ) {
			return false;
		}

		return true;
	}

	protected function get_field( string $field ) {
		$id = get_the_ID();

		return $id ? get_field( $field, $id ) : false;
	}

	public function ppl_is_active(): bool {
		return (bool) get_field( PPL_Options_Meta::PPL_IS_ACTIVE, get_the_ID() ) ?? false;
	}

	public function render_sidebar_tool_ad(): ?Deferred_Component {
		$slot_id = get_field( Tool_Post_Settings_Meta::NAME . '_' . Tool_Post_Settings_Meta::AD_GROUP_SIDEBAR_MAIN, 'option' );

		if ( ! $slot_id ) {
			return null;
		}

		if ( ! $this->has_adsanity_ad_running( $slot_id ) ) {
			return null;
		}

		$ad = $this->render_ad( $slot_id, 'main-sidebar', true );

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::TAG     => 'div',
			Container_Controller::CLASSES => [
				'c-sidebar-post__sidebar-item',
				'c-sidebar-post__sidebar-item--tool-main-ad',
			],
			Container_Controller::CONTENT => $ad,
		] );
	}

	/**
	 * @return bool
	 */
	private function hide_sidebar_recommendations(): bool {
		if ( 'post' !== get_post_type() ) {
			return false;
		}

		return (bool) get_field( Post_Meta::HIDE_SIDEBAR_RECOMMENDATIONS );
	}

	/**
	 * @return bool
	 */
	public function is_ppl_focused_content(): bool {
		return (bool) get_field( Post_Meta::PPL_FOCUSED_CONTENT );
	}
}
