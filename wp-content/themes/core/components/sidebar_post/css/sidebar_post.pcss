/* -----------------------------------------------------------------------------
 *
 * Component: Sidebar Post
 *
 * ----------------------------------------------------------------------------- */

.c-sidebar-post {
	display: none;
	grid-auto-rows: 1fr;
	grid-template-rows: 1fr;
	grid-row-gap: var(--spacer-30);
	/* margin-bottom: var(--spacer-80); */

	@media (--viewport-large) {
		display: grid;
	}

	&__sidebar-item {
		position: sticky;
		top: calc(var(--header-size) + var(--spacer-30));
		border-top: 1px solid var(--color-neutral-20);
		border-bottom: 1px solid var(--color-neutral-20);

		&--with-tag {
			border-top: 0;
		}

		&--ad {
			border-top: 0;
			border-bottom: 0;
		}

		&--newsletter {
			.b-inline-newsletter {
				margin-top: 0;
				margin-bottom: 0;

				.b-inline-newsletter__container {
					padding-bottom: 0;
					padding-top: 0;
					border: 0;
					grid-template-columns: 1fr;
					gap: var(--spacer-20);

					> *:not(.b-inline-newsletter__tag-container) {
						@media (--viewport-medium) {
							padding-right: 0;
							padding-left: 0;
						}
					}

					.b-inline-newsletter__tag-container {
						grid-column: span 1;
					}

					.b-inline-newsletter__image-container {
						height: 120px;
						padding-right: 0;
						padding-left: 0;
					}

					form {
						display: block;

						.gform_footer {
							padding-top: 0;
							padding-right: 0;
							display: block;
						}
					}
				}
			}

			.gform_wrapper {
				width: 100%;
				padding-top: var(--spacer-20);
				padding-bottom: var(--spacer-20);
			}
		}

		&--related-posts {
			display: flex;
			flex-direction: column;

			.c-sidebar-post__sidebar-item__related-post {
				padding-top: var(--spacer-30);
				padding-bottom: var(--spacer-30);
				display: grid;
				align-items: center;
				grid-template-columns: var(--spacer-70) 1fr;
				grid-column-gap: var(--spacer-20);

				&--no-image {
					grid-template-columns: 1fr;
				}

				&-title {
					@mixin t-display-xxx-small;
					font-size: var(--font-size-body-small);
					transition: var(--transition);
				}

				&-thumbnail {
					height: var(--spacer-70);
					overflow: hidden;

					&-img {
						height: var(--spacer-70);
						background-size: cover;
						background-position: center;
						transition: var(--transition);
						border-radius: var(--border-radius-media);
					}
				}

				&:not(:last-child) {
					border-bottom: 1px solid var(--color-neutral-20);
				}

				&:hover, &:focus {
					.c-sidebar-post__sidebar-item__related-post-title {
						color: var(--color-primary-70);
					}

					.c-sidebar-post__sidebar-item__related-post-thumbnail-img {
						transform: scale(1.1);
					}
				}
			}
		}

		&__tag-container {
			width: 100%;
			border-bottom: 1px solid var(--color-neutral-20);
			display: flex;
			align-items: center;
			flex-direction: row;
			justify-content: center;
		}

		&--toc-menu {
			transition: var(--transition);
			padding-bottom: 20px;
			border-bottom: none;
			border-top: none;

			.c-sidebar-post__sidebar-item--links-menu__container {
				margin-right: 8px;
			}

			&:has(.c-table-of-contents--full-height) {
				max-height: fit-content !important;
			}
		}

		&--ad {
			border-top: 0;
			border-bottom: 0;
		}

		&--main-ad {
			margin-bottom: 30vh;

			.c-ad-block-inline {
				margin-top: 0;
			}
		}

		&--ppl-advisor {
			padding: var(--spacer-20) var(--spacer-10);
			border-bottom: 1px solid var(--color-neutral-20);
			border-top: 1px solid var(--color-neutral-20);
			margin-top: var(--spacer-30);

			@media (--viewport-full) {
				padding: var(--spacer-30) var(--spacer-40);
			}

			&__title {
				display: block;
				position: relative;
				background-color: var(--color-neutral-20);
				border-radius: 8px;
				padding: var(--spacer-10) var(--spacer-20);
				font-weight: var(--font-weight-semibold);
				text-align: center;
				margin-bottom: var(--spacer-30);

				@media (--viewport-full) {
					.item-single__content & {
						width: 60%;
						margin-left: auto;
						margin-right: auto;
					}

				}

				&:before {
					content: "";
					position: absolute;
					top: calc(100% - 1px);
					left: var(--spacer-60);
					width: 0;
					height: 0;
					border-left: 5px solid transparent;
					border-right: 5px solid transparent;
					border-top: 10px solid var(--color-neutral-20);

					.c-sidebar-post__content & {
						left: 40%;
					}
				}
			}

			&__info-wrapper {
				display: flex;
				align-items: center;
				justify-content: center;

				> img {
					margin: 0;
					border-radius: 50%;
					width: 64px;
					height: 64px;
				}

				.c-sidebar-post__sidebar-item--ppl-advisor__info {
					margin-left: var(--spacer-20);
				}

				.c-sidebar-post__sidebar-item--ppl-advisor__avatar {
					margin-top: var(--spacer-10);

					&__img {
						width: auto;
						max-width: 100px;
						height: 20px;
						background-size: 100%;
						background-repeat: no-repeat;
					}
				}
			}

			&__info {
				&__advisor-title {
					font-size: 14px;
					font-weight: 600;
					width: 100px;
					margin-top: var(--spacer-10);
				}
			}

			&__content__description {
				text-align: center;
				margin-bottom: var(--spacer-30);
				margin-top: var(--spacer-30);
			}

			&__cta {
				text-align: center;

				&__link {
					.c-sidebar-post & {
						width: 100%;
						padding: 10px var(--spacer-30);
					}
				}
			}

			.c-sidebar-post__sidebar-item {
				border-top: unset;
				border-bottom: unset;
			}
		}

		&--tool-main-ad {
			margin-top: var(--spacer-30);
			border-top: none;
			border-bottom: none;
		}
	}

	&__item-container {
		max-width: var(--grid-sidebar-col);
		padding-bottom: var(--spacer-30);

		&:first-of-type {
			&:has(.c-sidebar-post__sidebar-item--3-pack-tool-highlight) {
				border-top: none;
			}
		}
	}

	&--fixed {
		> div:first-of-type {
			border-top: 0;
			z-index: 2;
			top: calc(var(--header-size) + var(--spacer-20));
			padding-top: 0;
			padding-bottom: 0;
			background-color: var(--color-neutral-0);
			position: sticky;
			transition: top .3s ease;

			&::before {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				top: calc(calc(var(--header-size) + var(--spacer-60)) * -1);
				height: calc(var(--header-size) + var(--spacer-60));
				background-color: var(--color-neutral-0);
				z-index: 1;
			}

			.c-sidebar-post__sidebar-item {
				z-index: 2;
			}
		}

		.c-sidebar-post__sidebar-item.c-sidebar-post__sidebar-item--toc-menu {
			display: block;

			.c-sidebar-post__sidebar-item--links-menu__container {
				display: block;
			}
		}

		.c-sidebar-post__sidebar-item .c-sidebar-post__sidebar-item--ad {
			display: none;

			@media (--viewport-full) and (min-height: 640px) {
				display: block;
			}
		}

		.c-sidebar-post__sidebar-item-container:not(:has(.c-sidebar-post__sidebar-item--toc-menu, .c-sidebar-post__sidebar-item--ad)) {
			display: none;

			@media (--viewport-full) and (min-height: 840px) {
				display: unset;
			}
		}

		.b-inline-newsletter__image-container {
			@media (--viewport-full) and (min-height: 840px) {
				display: none;
			}

			@media (--viewport-full) and (min-height: 1000px) {
				display: block;
			}
		}
	}

	&__floating-container {
		margin: var(--spacer-70) auto var(--spacer-70);

		@media (--viewport-large) {
			margin: 0 auto;
		}

		.c-ad-block-inline {
			margin-top: 0;
		}

		&__close-button {
			display: none;
			position: absolute;
			top: 0;
			right: 0;
			z-index: 1;

			&:before {
				background-color: var(--color-background-light);
				border-radius: var(--border-radius-circle);
				color: var(--color-black);
				content: var(--icon-close);
				font-family: var(--font-family-core-icons);
				font-size: var(--font-size-body-small);
				line-height: 1;
				text-align: center;
				transition: var(--transition);
				padding: calc(var(--spacer-10) / 2);
			}
		}
	}

	.c-ad-block-inline .c-ad-block__logo-img {
		max-width: 220px;
	}

	.c-ad-block--layout-inline .c-ad-block__description {
		@media (--viewport-full) {
			padding-left: var(--spacer-20);
			padding-right: var(--spacer-20);
		}
	}

	.item-single--perfect-listicle-layout & {
		padding-bottom: var(--spacer-80);

		@media (--viewport-medium) and (--viewport-large-max) {
			.c-sidebar-post__sidebar-item--ppl-advisor__content__description {
				display: none;
			}

			.c-sidebar-post__sidebar-item--ppl-advisor__info-wrapper {
				margin-bottom: var(--spacer-20);
			}
		}

		@media (max-height: 799px) {
			.c-sidebar-post__sidebar-item--ppl-advisor__content__description {
				display: none;
			}

			.c-sidebar-post__sidebar-item--ppl-advisor__info-wrapper {
				margin-bottom: var(--spacer-20);
			}
		}
	}

	.b-inline-newsletter .b-inline-newsletter__image-container {
		@media (max-height: 900px) {
			display: none;
		}
	}

	&--hide-toc {
		grid-template-rows: 0 1fr !important;
		grid-row-gap: 0 !important;

		.c-sidebar-post__sidebar-item {
			&--toc-menu {
				max-height: 0;
				height: 0;

				.c-table-of-contents {
					display: none;
				}
			}
		}

		&.c-sidebar-post--fixed {
			.c-sidebar-post__sidebar-item {
				top: calc(var(--header-size) + var(--spacer-20));
			}

			.c-sidebar-post__item-container {
				&:not(:last-of-type) {
					margin-bottom: var(--spacer-30);
				}
			}
		}
	}
}

.item-single--in-depth-reviews-layout {
	.c-sidebar-post {
		&--fixed {
			> div:first-of-type {
				&::before {
					content: none;
				}
			}
		}

		&__sidebar-video {
			&:not(:only-child) {
				margin-top: var(--spacer-40);
			}

			&__close-button {
				border: none;
				background-color: var(--color-neutral-10);
				width: 24px;
				height: 24px;
				position: absolute;
				z-index: 1;
				right: 0;
				text-align: center;
				border-radius: 50%;
				margin-top: -12px;
				margin-right: -12px;

				.icon {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					font-size: var(--font-size-body);
				}
			}

			.c-video {
				&__trigger-label {
					display: none;
				}

				&__trigger-action {
					display: flex;
					width: auto;
					justify-content: center;
					top: 50%;
					transform: translateY(-50%);
					bottom: auto;
				}

				&__trigger-icon {
					margin: 0;
					width: 37px;
					height: 37px;
					background-color: var(--color-secondary);

					&:before {
						font-size: 17px;
						line-height: 37px;
						color: var(--color-white);
					}
				}
			}
		}

		.c-table-of-contents {
			padding-top: var(--spacer-30);
			padding-bottom: var(--spacer-30);
		}
	}
}
