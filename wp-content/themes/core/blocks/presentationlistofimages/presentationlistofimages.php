<?php

declare( strict_types=1 );

use Tribe\Project\Blocks\Types\Presentation_List_of_Images\Presentation_List_of_Images_Model;

/**
 * @var array $block Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$model = new Presentation_List_of_Images_Model( $block );

$preview_image = $model->get( 'preview_image', '' );

/**
 * Render a block preview template when adding a new block, else render the full block template
 */
if ( ! empty( $preview_image ) ) {
	get_template_part( 'blocks/preview/preview', null, [ 'preview_image' => $preview_image ] );
} else {
	get_template_part( 'components/blocks/presentation_list_of_images/presentation_list_of_images', null, $model->get_data() );
}
