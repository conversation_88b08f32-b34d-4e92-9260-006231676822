<?php

declare( strict_types=1 );

use Tribe\Project\Blocks\Types\Presentation_Cards\Presentation_Cards_Model;

/**
 * @var array $block Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$model = new Presentation_Cards_Model( $block );

$preview_image = $model->get( 'preview_image', '' );

/**
 * Render a block preview template when adding a new block, else render the full block template
 */
if ( ! empty( $preview_image ) ) {
	get_template_part( 'blocks/preview/preview', null, [ 'preview_image' => $preview_image ] );
} else {
	get_template_part( 'components/blocks/presentation_cards/presentation_cards', null, $model->get_data() );
}
