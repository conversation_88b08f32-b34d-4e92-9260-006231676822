/**
 * @module
 * @exports init
 * @description Initializes all components found in the components directory of the theme
 */

import header from 'components/header';
import accordion from 'components/accordion';
import accordionAdvanced from 'components/blocks/accordion_advanced';
import advancedContentFilter from 'components/blocks/advanced_content_filter';
import banner from 'components/banner';
import card from 'components/card';
// import comments from 'components/comments';
import share from 'components/share';
import slider from 'components/slider';
import tabs from 'components/tabs';
import video from 'components/video';
import dialog from 'components/dialog';
import gallery from 'components/blocks/gallery_grid';
import progressBar from 'components/progress_bar';
import postFloatingBar from 'components/post_floating_bar';
import postNavigationBar from 'components/post_navigation_bar';
import singlePost from 'components/routes/single';
import search from 'components/routes/search';
import countdown from 'components/countdown';
import countdownBlock from 'components/blocks/countdown';
import cardGrid from 'components/blocks/card_grid';
import contentChapter from 'components/blocks/content_chapter';
import adBlocks from 'components/ad_block/';
import screenshot_gallery from 'components/screenshot_gallery/';
import membershipNavigation from 'components/membership_navigation/';
import archiveFilters from 'components/archive_filters/';
import memberpressAccount from 'components/routes/account';
import videoGallery from 'components/blocks/video_gallery/js/video-gallery';
import inlineNewsletter from 'components/blocks/inline_newsletter/js/inline_newsletter';
import modal from '../core/components/modal';
import interstitialFirstClick from '../core/components/interstitial-first-click';
import postLoader from '../core/components/post-loader';
import pplSelector from 'components/blocks/ppl_selector/js/ppl_selector';
import pplInline from 'components/blocks/ppl_inline/js/ppl_inline';
import listicleBlocks from 'components/blocks/listicle/';
import category from 'components/routes/category';
import tableOfContent from 'components/blocks/table_of_contents';
import tableOfContentsComponent from 'components/table_of_contents';
import calculator from 'components/blocks/calculator';
import columns from 'components/blocks/columns';
import contentLoop from 'components/blocks/content_loop';
import sidebarPost from 'components/sidebar_post';

const init = () => {
	modal();
	header();
	columns();
	contentLoop();
	accordion();
	accordionAdvanced();
	advancedContentFilter();
	banner();
	card();
	share();
	slider();
	tabs();
	video();
	dialog();
	gallery();
	progressBar();
	postFloatingBar();
	postNavigationBar();
	singlePost();
	search();
	countdown();
	countdownBlock();
	cardGrid();
	contentChapter();
	adBlocks();
	screenshot_gallery();
	membershipNavigation();
	archiveFilters();
	memberpressAccount();
	videoGallery();
	inlineNewsletter();
	pplSelector();
	pplInline();
	listicleBlocks();
	category();
	tableOfContent();
	tableOfContentsComponent();
	interstitialFirstClick();
	postLoader();
	calculator();
	sidebarPost();

	console.info( 'SquareOne Theme: Initialized all components.' );
};

export default init;
